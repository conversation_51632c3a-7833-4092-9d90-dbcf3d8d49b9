package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.config.ReceptionSettingsDto;
import com.czb.hn.dto.recipients.EmailRecipientDto;
import com.czb.hn.dto.recipients.ReceptionMethodsDto;
import com.czb.hn.dto.recipients.SmsRecipientDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.NotificationType;
import com.czb.hn.service.business.ReceptionRulesEngine;
import com.czb.hn.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Reception Rules Engine Implementation
 * Implements complex logic for determining when and how notifications should be
 * sent
 */
@Service
@Slf4j
public class ReceptionRulesEngineImpl implements ReceptionRulesEngine {

    private static final Set<DayOfWeek> WORKDAYS = Set.of(
            DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY,
            DayOfWeek.THURSDAY, DayOfWeek.FRIDAY);

    private static final Set<DayOfWeek> WEEKENDS = Set.of(
            DayOfWeek.SATURDAY, DayOfWeek.SUNDAY);

    @Override
    public boolean shouldSendNotification(
            AlertResult alert,
            ReceptionSettingsDto settings,
            LocalDateTime lastNotificationTime) {

        LocalDateTime currentTime = LocalDateTime.now();

        try {
            validateReceptionSettings(settings);

            // Check if interval requirement is met
            if (!hasIntervalPassed(settings, lastNotificationTime, currentTime)) {
                log.debug("Notification interval not met for alert {}", alert != null ? alert.getId() : "no-alert");
                return false;
            }

            // Check if current time matches reception schedule
            if (!matchesReceptionSchedule(settings, currentTime)) {
                log.debug("Current time does not match reception schedule");
                return false;
            }

            // Check if within reception period
            boolean withinPeriod = isWithinReceptionPeriod(settings, currentTime);

            // For alerts, check if we should send during non-alert hours
            if (alert != null && !withinPeriod) {
                return settings.infoPush() != null && settings.infoPush();
            }

            return withinPeriod;

        } catch (Exception e) {
            log.error("Error evaluating notification rules: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public LocalDateTime calculateNextNotificationTime(
            ReceptionSettingsDto settings,
            LocalDateTime currentTime,
            LocalDateTime lastNotificationTime) {

        try {
            // Start with current time
            LocalDateTime nextTime = currentTime;

            // Apply interval constraint
            if (lastNotificationTime != null) {
                LocalDateTime earliestNext = lastNotificationTime.plusMinutes(settings.alertInterval());
                if (nextTime.isBefore(earliestNext)) {
                    nextTime = earliestNext;
                }
            }

            // Find next valid time slot based on reception schedule and period
            while (!isValidNotificationTime(settings, nextTime)) {
                nextTime = nextTime.plusMinutes(5); // Check every 5 minutes

                // Prevent infinite loop - max 7 days ahead
                if (nextTime.isAfter(currentTime.plusDays(7))) {
                    log.warn("Could not find valid notification time within 7 days");
                    return currentTime.plusHours(1); // Fallback
                }
            }

            return nextTime;

        } catch (Exception e) {
            log.error("Error calculating next notification time: {}", e.getMessage(), e);
            return currentTime.plusHours(1); // Fallback
        }
    }

    @Override
    public NotificationType determineNotificationType(
            AlertResult alert,
            ReceptionSettingsDto settings,
            boolean isWithinAlertPeriod) {

        if (alert != null) {
            // We have an actual alert
            if (isWithinAlertPeriod) {
                return NotificationType.ALERT;
            } else if (settings.infoPush() != null && settings.infoPush()) {
                return NotificationType.INFO_PUSH;
            }
        } else {
            // No alert case
            if (settings.noAlertNotification() != null && settings.noAlertNotification()) {
                return NotificationType.NO_ALERT;
            }
        }

        return null; // No notification should be sent
    }

    @Override
    public boolean isWithinReceptionPeriod(ReceptionSettingsDto settings, LocalDateTime currentTime) {
        if (settings.receptionPeriod() == null) {
            return true; // No period restriction
        }

        try {
            LocalTime currentTimeOfDay = currentTime.toLocalTime();
            LocalTime startTime = LocalTime.parse(settings.receptionPeriod().start());
            LocalTime endTime = LocalTime.parse(settings.receptionPeriod().end());

            // Handle 24:00 as end of day
            if ("24:00".equals(settings.receptionPeriod().end())) {
                endTime = LocalTime.of(23, 59, 59);
            }

            // Handle overnight periods (e.g., 22:00 - 06:00)
            if (startTime.isAfter(endTime)) {
                return currentTimeOfDay.isAfter(startTime) || currentTimeOfDay.isBefore(endTime);
            } else {
                return !currentTimeOfDay.isBefore(startTime) && !currentTimeOfDay.isAfter(endTime);
            }

        } catch (Exception e) {
            log.error("Error checking reception period: {}", e.getMessage(), e);
            return true; // Default to allow if parsing fails
        }
    }

    @Override
    public boolean matchesReceptionSchedule(ReceptionSettingsDto settings, LocalDateTime currentTime) {
        if (settings.receptionTime() == null) {
            return true; // No schedule restriction
        }

        DayOfWeek currentDay = currentTime.getDayOfWeek();

        return switch (settings.receptionTime()) {
            case "DAILY" -> true;
            case "WORKDAYS" -> WORKDAYS.contains(currentDay);
            case "HOLIDAYS" -> WEEKENDS.contains(currentDay);
            default -> {
                log.warn("Unknown reception time: {}", settings.receptionTime());
                yield true; // Default to allow
            }
        };
    }

    @Override
    public List<RecipientInfo> extractRecipients(ReceptionSettingsDto settings) {
        return extractRecipients(settings.receptionMethods());
    }

    @Override
    public List<RecipientInfo> extractRecipients(ReceptionMethodsDto dto) {
        List<RecipientInfo> recipients = new ArrayList<>();
        if (dto == null) {
            return recipients;
        }
        try {
            // Extract email recipients
            if (dto.email() != null && dto.email().enabled() && dto.email().recipients() != null) {

                for (EmailRecipientDto emailRecipient : dto.email()
                        .recipients()) {
                    recipients.add(new RecipientInfo(
                            emailRecipient.name(),
                            emailRecipient.email(),
                            null, // No phone for email recipients
                            emailRecipient.name(), // Use name as username
                            emailRecipient.isActive(), // Email enabled
                            false // SMS not enabled
                    ));
                }
            }

            // Extract SMS recipients
            if (dto.sms() != null && dto.sms().enabled() && dto.sms().recipients() != null) {

                for (SmsRecipientDto smsRecipient : dto.sms()
                        .recipients()) {
                    recipients.add(new RecipientInfo(
                            smsRecipient.name(),
                            null, // No email for SMS recipients
                            smsRecipient.phone(),
                            smsRecipient.name(), // Use name as username
                            false, // Email not enabled
                            smsRecipient.isActive() // SMS enabled
                    ));
                }
            }

        } catch (Exception e) {
            log.error("Error extracting recipients: {}", e.getMessage(), e);
        }

        return recipients;
    }

    @Override
    public boolean hasIntervalPassed(
            ReceptionSettingsDto settings,
            LocalDateTime lastNotificationTime,
            LocalDateTime currentTime) {

        if (lastNotificationTime == null) {
            return true; // No previous notification
        }

        if (settings.alertInterval() == null) {
            return true; // No interval restriction
        }

        LocalDateTime nextAllowedTime = lastNotificationTime.plusMinutes(settings.alertInterval());
        return !currentTime.isBefore(nextAllowedTime);
    }

    @Override
    public void validateReceptionSettings(ReceptionSettingsDto settings) {
        if (settings == null) {
            throw new IllegalArgumentException("Reception settings cannot be null");
        }

        // Validate reception time
        if (settings.receptionTime() != null &&
                !List.of("DAILY", "WORKDAYS", "HOLIDAYS").contains(settings.receptionTime())) {
            throw new IllegalArgumentException("Invalid reception time: " + settings.receptionTime());
        }

        // Validate alert interval
        if (settings.alertInterval() != null && settings.alertInterval() < 30) {
            throw new IllegalArgumentException("Alert interval must be at least 30 minutes");
        }

        // Validate reception methods
        if (settings.receptionMethods() == null) {
            throw new IllegalArgumentException("Reception methods cannot be null");
        }
    }

    @Override
    public NotificationScheduleResult evaluateNotificationSchedule(
            AlertResult alert,
            ReceptionSettingsDto settings,
            LocalDateTime lastNotificationTime,
            LocalDateTime currentTime) {

        try {
            validateReceptionSettings(settings);

            // Check interval requirement
            if (!hasIntervalPassed(settings, lastNotificationTime, currentTime)) {
                return NotificationScheduleResult.skip(
                        "Alert interval not met (minimum " + settings.alertInterval() + " minutes)");
            }

            // Check if within reception period
            boolean withinPeriod = isWithinReceptionPeriod(settings, currentTime);
            boolean matchesSchedule = matchesReceptionSchedule(settings, currentTime);

            if (!matchesSchedule) {
                LocalDateTime nextValidTime = calculateNextNotificationTime(settings, currentTime,
                        lastNotificationTime);
                return NotificationScheduleResult.schedule(nextValidTime,
                        alert != null ? NotificationType.ALERT : NotificationType.NO_ALERT);
            }

            // Determine notification type
            NotificationType notificationType = determineNotificationType(alert, settings, withinPeriod);

            if (notificationType == null) {
                return NotificationScheduleResult.skip("No notification type determined");
            }

            // Calculate scheduled time
            LocalDateTime scheduledTime = withinPeriod ? currentTime
                    : calculateNextNotificationTime(settings, currentTime, lastNotificationTime);

            return NotificationScheduleResult.schedule(scheduledTime, notificationType);

        } catch (Exception e) {
            log.error("Error evaluating notification schedule: {}", e.getMessage(), e);
            return NotificationScheduleResult.skip("Error in evaluation: " + e.getMessage());
        }
    }

    /**
     * Check if a specific time is valid for notification based on settings
     */
    private boolean isValidNotificationTime(ReceptionSettingsDto settings, LocalDateTime time) {
        return matchesReceptionSchedule(settings, time) && isWithinReceptionPeriod(settings, time);
    }

    @Override
    public TimeRangeResult calculateAlertTimeRange(
            NotificationType notificationType,
            ReceptionSettingsDto settings,
            LocalDateTime currentTime,
            LocalDateTime lastNotificationTime) {

        LocalDateTime startTime;
        LocalDateTime endTime = currentTime;

        switch (notificationType) {
            case ALERT:
                // 对于预警通知，时间段为当前时间减去配置的间隔到当前时间
                startTime = currentTime.minusMinutes(settings.alertInterval());
                break;

            case INFO_PUSH:
                // 对于信息补推，时间段为上次通知时间到当前时间
                if (lastNotificationTime != null) {
                    startTime = lastNotificationTime;
                } else {
                    // 如果没有上次通知时间，使用默认间隔
                    startTime = currentTime.minusMinutes(settings.alertInterval());
                }
                break;

            case NO_ALERT:
                // 对于无预警通知，时间段为前一天21:00到当天21:00
                LocalDateTime yesterday21 = currentTime.minusDays(1).withHour(21).withMinute(0).withSecond(0)
                        .withNano(0);
                LocalDateTime today21 = currentTime.withHour(21).withMinute(0).withSecond(0).withNano(0);

                // 如果当前时间在21:00之前，则结束时间为今天21:00
                // 如果当前时间在21:00之后，则结束时间为明天21:00
                if (currentTime.isBefore(today21)) {
                    startTime = yesterday21;
                    endTime = today21;
                } else {
                    startTime = today21;
                    endTime = currentTime.plusDays(1).withHour(21).withMinute(0).withSecond(0).withNano(0);
                }
                break;

            default:
                // 默认使用预警间隔
                startTime = currentTime.minusMinutes(settings.alertInterval());
                break;
        }

        // 格式化时间段
        String formattedRange = String.format("%s - %s",
                DateTimeUtil.formatToStandardString(startTime),
                DateTimeUtil.formatToStandardString(endTime));

        return TimeRangeResult.of(startTime, endTime, formattedRange);
    }
}
