package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;

import java.time.LocalDateTime;

/**
 * Alert Notification Scheduler Interface
 * Manages the scheduling and processing of alert notifications based on
 * reception settings
 * Decouples alert generation from notification delivery
 */
public interface AlertNotificationScheduler {

    /**
     * Process pending notifications (scheduled task)
     * Checks for notifications ready to be sent and processes them
     */
    void processPendingNotifications();

    /**
     * Schedule no-alert notifications for configurations without recent alerts
     * 
     * @param configuration Alert configuration to check for no-alert notifications
     * @param now Current time
     */
    void scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now);

    /**
     * Process notifications ready for sending
     *
     * @param now Current time
     * 
     * @return Number of notifications processed
     */
    int processReadyNotifications(LocalDateTime now);

    /**
     * Retry failed notifications
     *
     * @param now Current time
     * 
     * @return Number of notifications retried
     */
    int retryFailedNotifications(LocalDateTime now);

}
