package com.czb.hn.service.business.impl;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.ShareLinkDto;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.config.ReceptionSettingsDto;
import com.czb.hn.dto.recipients.EmailConfigDto;
import com.czb.hn.dto.recipients.ReceptionMethodsDto;
import com.czb.hn.dto.recipients.SmsConfigDto;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.NotificationStatus;
import com.czb.hn.enums.NotificationType;
import com.czb.hn.jpa.securadar.repository.AlertNotificationQueueRepository;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertNotificationScheduler;
import com.czb.hn.service.business.AlertPushService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.service.business.ReceptionRulesEngine;

import com.czb.hn.service.share.ShareLinkService;
import com.czb.hn.util.EmailTemplateUtil;
import com.czb.hn.util.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Alert Notification Scheduler Implementation
 * Handles the scheduling and processing of alert notifications based on
 * reception settings
 */
@Service
@Slf4j
public class AlertNotificationSchedulerImpl implements AlertNotificationScheduler {

    @Value("${alert.message.sms.template.content}")
    private String smsContentTemplate;
    @Value("${alert.message.sms.template.noWarnContent}")
    private String smsNoWarnContentTemplate;
    @Value("${alert.message.email.template.subjectContent}")
    private String emailSubjectTemplate;
    @Value("${alert.message.email.template.noWarnSubjectContent}")
    private String emailNoWarnSubjectTemplate;
    @Value("${alert.message.webHookUrl}")
    private String webHookUrl;

    @Autowired
    private AlertNotificationQueueRepository notificationQueueRepository;

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private AlertConfigurationConsumerService alertConfigConsumerService;

    @Autowired
    private ReceptionRulesEngine receptionRulesEngine;

    @Autowired
    private AlertPushService alertPushService;

    @Autowired
    private PlanService planService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ShareLinkService shareLinkService;

    @Autowired
    private EmailTemplateUtil emailTemplateUtil;

    @Override
    @Scheduled(fixedDelay = 60000) // Every minute
    public void processPendingNotifications() {
        log.debug("Starting to process pending notifications");

        try {

            LocalDateTime now = LocalDateTime.now();

            // Schedule new notifications for recent alerts
            int scheduledCount = scheduleNewNotifications(now);
            if (scheduledCount > 0) {
                log.info("Scheduled {} new notifications", scheduledCount);
            }

            // Process ready notifications
            int processedCount = processReadyNotifications(now);
            if (processedCount > 0) {
                log.info("Processed {} notifications", processedCount);
            }

            // Retry failed notifications
            int retriedCount = retryFailedNotifications(now);
            if (retriedCount > 0) {
                log.info("Retried {} notifications", retriedCount);
            }

            if (processedCount > 0 || retriedCount > 0 ||
                    scheduledCount > 0) {
                log.info("Notification processing summary - Processed: {}, Retried: {}, , Scheduled: {}",
                        processedCount, retriedCount, scheduledCount);
            }

        } catch (Exception e) {
            log.error("Error in notification processing cycle", e);
        }
    }

    @Override
    public void scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
        log.debug("Checking no-alert notifications for configuration: {}", configuration.id());

        try {
            if (configuration.receptionSettings() == null ||
                    configuration.receptionSettings().noAlertNotification() == null ||
                    !configuration.receptionSettings().noAlertNotification()) {
                return; // No-alert notifications not enabled
            }

            // Check if there were any alerts in the last interval period
            LocalDateTime intervalStart = now.minusMinutes(
                    configuration.receptionSettings().alertInterval() != null
                            ? configuration.receptionSettings().alertInterval()
                            : 60);

            boolean hasRecentAlerts = alertResultRepository.existsByConfigurationIdAndWarningTimeBetween(
                    configuration.id(), intervalStart, now);

            if (hasRecentAlerts) {
                log.debug("Recent alerts found for configuration {}, no-alert notification not needed",
                        configuration.id());
                return;
            }

            // Check if no-alert notification already scheduled for this period
            if (notificationQueueRepository.existsNoAlertNotificationInTimeRange(
                    configuration.id(), intervalStart, now.plusHours(1))) {
                log.debug("No-alert notification already scheduled for configuration {}", configuration.id());
                return;
            }

            // Get last notification time
            LocalDateTime lastNotificationTime = getLastNotificationTime(configuration.id());

            // Evaluate notification schedule for no-alert case
            ReceptionRulesEngine.NotificationScheduleResult scheduleResult = receptionRulesEngine
                    .evaluateNotificationSchedule(
                            null, configuration.receptionSettings(), lastNotificationTime, now);

            if (!scheduleResult.shouldSchedule() || scheduleResult.notificationType() != NotificationType.NO_ALERT) {
                log.debug("No-alert notification not scheduled for configuration {}: {}",
                        configuration.id(), scheduleResult.reason());
                return;
            }

            // Extract recipients
            List<ReceptionRulesEngine.RecipientInfo> recipients = receptionRulesEngine
                    .extractRecipients(configuration.receptionSettings());

            if (recipients.isEmpty()) {
                log.warn("No recipients found for no-alert notification, configuration: {}", configuration.id());
                return;
            }

            // Create no-alert notification queue entry
            AlertNotificationQueue notification = AlertNotificationQueue.builder()
                    .planId(configuration.planId())
                    .configurationId(configuration.id())
                    .enterpriseId(configuration.enterpriseId())
                    .scheduledTime(scheduleResult.scheduledTime())
                    .status(NotificationStatus.PENDING)
                    .recipients(serializeRecipients(recipients))
                    .notificationType(NotificationType.NO_ALERT)
                    .receptionSettings(serializeReceptionSettings(configuration.receptionSettings()))
                    .createdBy("SCHEDULER")
                    .build();

            notificationQueueRepository.save(notification);
            log.info("Scheduled no-alert notification for configuration {} at {}",
                    configuration.id(), scheduleResult.scheduledTime());

        } catch (Exception e) {
            log.error("Failed to schedule no-alert notifications for configuration {}", configuration.id(), e);
        }
    }

    @Override
    public int processReadyNotifications(LocalDateTime now) {
        log.debug("Processing ready notifications");

        try {
            List<AlertNotificationQueue> readyNotifications = notificationQueueRepository
                    .findReadyToProcess(NotificationStatus.PENDING, now);

            int processedCount = 0;
            for (AlertNotificationQueue notification : readyNotifications) {
                try {
                    processNotification(notification);
                    processedCount++;
                } catch (Exception e) {
                    log.error("Failed to process notification {}", notification.getId(), e);
                    notification.markAsFailed("Processing error: " + e.getMessage());
                    notificationQueueRepository.save(notification);
                }
            }

            return processedCount;

        } catch (Exception e) {
            log.error("Error processing ready notifications", e);
            return 0;
        }
    }

    @Override
    public int retryFailedNotifications(LocalDateTime now) {
        log.debug("Retrying failed notifications");

        try {
            List<AlertNotificationQueue> retryNotifications = notificationQueueRepository
                    .findReadyForRetry(NotificationStatus.FAILED, now);

            int retriedCount = 0;
            for (AlertNotificationQueue notification : retryNotifications) {
                try {
                    processNotification(notification);
                    retriedCount++;
                } catch (Exception e) {
                    log.error("Failed to retry notification {}", notification.getId(), e);
                    notification.markAsFailed("Retry error: " + e.getMessage());
                    notificationQueueRepository.save(notification);
                }
            }

            return retriedCount;

        } catch (Exception e) {
            log.error("Error retrying failed notifications", e);
            return 0;
        }
    }

    /**
     * Process a single notification
     */
    private void processNotification(AlertNotificationQueue notification) {
        log.debug("Processing notification {}", notification.getId());

        try {
            // Mark as processing
            notification.markAsProcessing();
            notificationQueueRepository.save(notification);

            // Deserialize recipients
            List<ReceptionRulesEngine.RecipientInfo> recipients = deserializeRecipients(notification.getRecipients());

            // Process based on notification type
            if (notification.isNoAlertNotification()) {
                processNoAlertNotification(notification, recipients);
            } else {
                processAlertNotification(notification, recipients);
            }

            // Mark as completed
            notification.markAsCompleted();
            notificationQueueRepository.save(notification);

            log.info("Successfully processed notification {}", notification.getId());

        } catch (Exception e) {
            log.error("Failed to process notification {}", notification.getId(), e);
            notification.markAsFailed("Processing failed: " + e.getMessage());
            notificationQueueRepository.save(notification);
            throw e;
        }
    }

    /**
     * Process no-alert notification
     */
    private void processNoAlertNotification(AlertNotificationQueue notification,
            List<ReceptionRulesEngine.RecipientInfo> recipients) {

        log.debug("Processing no-alert notification for configuration {}", notification.getConfigurationId());

        // acquire plan
        Long planId = notification.getPlanId();
        if (planId == null) {
            log.error("notification is not plan id");
            return;
        }
        PlanDTO plan = planService.getPlanById(planId);

        // 反序列化接收设置
        ReceptionSettingsDto receptionSettings = null;
        if (notification.getReceptionSettings() != null) {
            try {
                receptionSettings = JsonUtil.fromJson(notification.getReceptionSettings(), ReceptionSettingsDto.class);
            } catch (Exception e) {
                log.warn("Failed to deserialize reception settings for no-alert notification {}, using default",
                        notification.getId(), e);
                // 使用默认设置
                EmailConfigDto emailConfig = new EmailConfigDto(false, null);
                SmsConfigDto smsConfig = new SmsConfigDto(true, null);
                ReceptionMethodsDto receptionMethods = new ReceptionMethodsDto(
                        emailConfig, smsConfig);
                receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, receptionMethods, false);
            }
        } else {
            // 如果没有设置，使用默认值
            EmailConfigDto emailConfig = new EmailConfigDto(false, null);
            SmsConfigDto smsConfig = new SmsConfigDto(true, null);
            ReceptionMethodsDto receptionMethods = new ReceptionMethodsDto(
                    emailConfig, smsConfig);
            receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, receptionMethods, false);
        }

        // 计算无预警通知的时间段
        LocalDateTime currentTime = LocalDateTime.now();
        ReceptionRulesEngine.TimeRangeResult timeRange = receptionRulesEngine.calculateAlertTimeRange(
                NotificationType.NO_ALERT, receptionSettings, currentTime, notification.getCreatedAt());

        // Process push notifications for each recipient
        for (ReceptionRulesEngine.RecipientInfo recipient : recipients) {
            try {
                if (recipient.emailEnabled() && recipient.email() != null) {
                    try {
                        String subject = createNoAlertEmailSubject(plan, timeRange);
                        javax.mail.internet.MimeMultipart emailContent = createNoAlertEmailMultipart(plan, timeRange);
                        alertPushService.sendComplexEmailNotification(recipient.email(), subject, emailContent);
                    } catch (Exception e) {
                        log.error("Failed to create HTML no-alert email content, falling back to simple content", e);
                        // Fallback to simple email
                        String subject = createNoAlertEmailSubject(plan, timeRange);
                        String simpleContent = String.format("根据智眸舆情监测平台在%s的持续扫描与分析，未发现【%s】方案的舆情预警信息。系统将持续进行实时监控。",
                                timeRange.formattedRange(), plan.name());
                        alertPushService.sendEmailNotification(recipient.email(), subject, simpleContent);
                    }
                }

                if (recipient.smsEnabled() && recipient.phone() != null) {
                    String message = createNoAlertSmsMessage(plan, timeRange);
                    alertPushService.sendSmsNotification(recipient.phone(), message);
                }

                // Create push record for no-alert notification
                alertPushService.createNoAlertPushRecord(notification, recipient);

            } catch (Exception e) {
                log.error("Failed to send no-alert notification to recipient {}", recipient.name(), e);
            }
        }

        log.info("Completed processing no-alert notification for plan {} with time range {}",
                plan.name(), timeRange.formattedRange());
    }

    /**
     * Process alert-based notification
     */
    private void processAlertNotification(AlertNotificationQueue notification,
            List<ReceptionRulesEngine.RecipientInfo> recipients) {

        log.debug("Processing batch alert notification {}", notification.getId());

        // Get all alerts associated with this notification
        List<AlertResult> alerts = alertResultRepository.findByAlertNotificationQueueId(notification.getId());

        if (alerts.isEmpty()) {
            log.warn("No alerts found for notification queue {}", notification.getId());
            return;
        }

        log.info("Processing batch notification for {} alerts", alerts.size());

        // acquire plan
        Long planId = notification.getPlanId();
        if (planId == null) {
            log.error("notification is not plan id");
            return;
        }
        PlanDTO plan = planService.getPlanById(planId);
        LocalDateTime scheduledTime = notification.getScheduledTime();

        // 反序列化接收设置
        ReceptionSettingsDto receptionSettings = null;
        if (notification.getReceptionSettings() != null) {
            try {
                receptionSettings = JsonUtil.fromJson(notification.getReceptionSettings(), ReceptionSettingsDto.class);
            } catch (Exception e) {
                log.warn("Failed to deserialize reception settings for notification {}, using default interval",
                        notification.getId(), e);
                // 使用默认设置
                receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, null, false);
            }
        } else {
            // 如果没有设置，使用默认值
            receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, null, false);
        }

        // Send one notification per recipient type (SMS/Email), but create push records
        // for each alert
        for (ReceptionRulesEngine.RecipientInfo recipient : recipients) {
            try {
                // Send batch notification (one SMS/Email for all alerts)
                if (recipient.smsEnabled() && recipient.phone() != null) {
                    String batchMessage = createBatchSmsMessage(alerts, plan);
                    alertPushService.sendSmsNotification(recipient.phone(), batchMessage);
                }

                if (recipient.emailEnabled() && recipient.email() != null) {
                    try {
                        String batchSubject = createBatchEmailSubject(alerts, plan);
                        javax.mail.internet.MimeMultipart emailContent = createBatchEmailMultipart(alerts, plan,
                                receptionSettings, scheduledTime);
                        alertPushService.sendComplexEmailNotification(recipient.email(), batchSubject, emailContent);
                    } catch (Exception e) {
                        log.error("Failed to create HTML email content, falling back to simple subject", e);
                        // Fallback to simple email with just subject
                        String batchSubject = createBatchEmailSubject(alerts, plan);
                        String simpleContent = String.format("【%s】方案新增%d条预警，请登录系统查看详情。", plan.name(), alerts.size());
                        alertPushService.sendEmailNotification(recipient.email(), batchSubject, simpleContent);
                    }
                }

                // Create push records for each alert 一批创建
                alertPushService.createPushRecordsForAlert(alerts.getFirst(), recipient);

            } catch (Exception e) {
                log.error("Failed to send batch notification to recipient {}", recipient.name(), e);
            }
        }
    }

    /**
     * Get last successful alert notification time for a configuration
     * Used to calculate the time range for integrated alert scheduling
     */
    private LocalDateTime getLastSuccessfulAlertNotificationTime(Long configurationId) {
        try {
            // 查询该配置最近的成功预警或信息补推通知时间
            return notificationQueueRepository.findLastSuccessfulAlertNotification(configurationId)
                    .map(AlertNotificationQueue::getScheduledTime)
                    .orElse(null);
        } catch (Exception e) {
            log.warn("Failed to get last successful alert notification time for configuration {}: {}",
                    configurationId, e.getMessage());
            return null;
        }
    }

    /**
     * Get last notification time for a configuration (for backward compatibility)
     */
    private LocalDateTime getLastNotificationTime(Long configurationId) {
        try {
            // 查询该配置最近的通知时间
            return notificationQueueRepository.findTopByConfigurationIdOrderByScheduledTimeDesc(configurationId)
                    .map(AlertNotificationQueue::getScheduledTime)
                    .orElse(null);
        } catch (Exception e) {
            log.warn("Failed to get last notification time for configuration {}: {}", configurationId, e.getMessage());
            return null;
        }
    }

    /**
     * Create batch SMS message for multiple alerts
     */
    private String createBatchSmsMessage(List<AlertResult> alerts, PlanDTO planDTO) {

        // "{address} 监控方案 {planName} 新增{number}条预警"
        return String.format(smsContentTemplate,
                webHookUrl,
                planDTO.name(), alerts.size());
    }

    /**
     * Create batch email subject for multiple alerts
     */
    private String createBatchEmailSubject(List<AlertResult> alerts, PlanDTO planDTO) {
        // 取最新一条预警标题
        AlertResult alert = alerts.getFirst();
        String title = alert.getTitle();
        String name = planDTO.name();
        return String.format(emailSubjectTemplate, name, alerts.size(), title);
    }

    /**
     * Create batch email content as MimeMultipart with HTML format and inline
     * images
     */
    private javax.mail.internet.MimeMultipart createBatchEmailMultipart(List<AlertResult> alerts, PlanDTO planDTO,
            ReceptionSettingsDto receptionSettings, LocalDateTime scheduledTime) throws Exception {
        String name = planDTO.name();

        // 获取预警时间段，使用ReceptionRulesEngine计算
        ReceptionRulesEngine.TimeRangeResult timeRange = receptionRulesEngine.calculateAlertTimeRange(
                NotificationType.ALERT, receptionSettings, scheduledTime, null);
        String timeRangeStr = timeRange.formattedRange();

        int size = alerts.size();

        // 敏感信息占比
        long sensitiveCount = alerts.stream()
                .filter(it -> InformationSensitivityType.SENSITIVE.equals(it.getInformationSensitivityType()))
                .count();
        String sensitiveRatioPercentage;

        if (size > 0) {
            double sensitiveRatio = (double) sensitiveCount / size;
            sensitiveRatioPercentage = String.format("%.1f%%", sensitiveRatio * 100);
        } else {
            sensitiveRatioPercentage = "0.0%";
        }

        // 中等及严重预警占比
        long warningNum = alerts.stream().filter(it -> AlertResult.WarningLevel.SEVERE.equals(it.getWarningLevel()) ||
                AlertResult.WarningLevel.MODERATE.equals(it.getWarningLevel())).count();

        String warningRatioPercentage = null;
        if (warningNum > 0) {
            double warnRatio = (double) warningNum / size;
            warningRatioPercentage = String.format("%.1f%%", warnRatio * 100);
        }

        // 创建分享链接
        ShareLinkDto shareLink = shareLinkService.createShareLink(webHookUrl, 60 * 60 * 1000L, "system");
        String shareCode = shareLink.shareCode();
        String url = webHookUrl.contains("?") ? webHookUrl + "&shareCode=" + shareCode
                : webHookUrl + "?shareCode=" + shareCode;

        // 使用EmailTemplateUtil创建HTML邮件内容
        return emailTemplateUtil.createAlertEmailContent(
                name,
                timeRangeStr,
                size,
                sensitiveRatioPercentage,
                warningRatioPercentage,
                url);
    }

    /**
     * Create no-alert email content as MimeMultipart with HTML format and inline
     * images
     */
    private javax.mail.internet.MimeMultipart createNoAlertEmailMultipart(PlanDTO planDTO,
            ReceptionRulesEngine.TimeRangeResult timeRange) throws Exception {
        String name = planDTO.name();
        String timeRangeStr = timeRange.formattedRange();

        // 使用EmailTemplateUtil创建HTML邮件内容
        return emailTemplateUtil.createNoAlertEmailContent(name, timeRangeStr);
    }

    /**
     * 检查当前时间是否在接收时段内
     */
    private boolean isWithinReceptionPeriod(ReceptionSettingsDto receptionSettings, LocalDateTime now) {
        if (receptionSettings.receptionPeriod() == null) {
            return true; // 如果没有设置时段，默认全天接收
        }

        String startTime = receptionSettings.receptionPeriod().start();
        String endTime = receptionSettings.receptionPeriod().end();

        if (startTime == null || endTime == null) {
            return true;
        }

        try {
            String[] startParts = startTime.split(":");
            String[] endParts = endTime.split(":");

            int startHour = Integer.parseInt(startParts[0]);
            int startMinute = Integer.parseInt(startParts[1]);
            int endHour = Integer.parseInt(endParts[0]);
            int endMinute = Integer.parseInt(endParts[1]);

            // 处理24:00的情况
            if (endHour == 24) {
                endHour = 0;
                endMinute = 0;
            }

            int currentHour = now.getHour();
            int currentMinute = now.getMinute();
            int currentTotalMinutes = currentHour * 60 + currentMinute;
            int startTotalMinutes = startHour * 60 + startMinute;
            int endTotalMinutes = endHour * 60 + endMinute;

            // 处理跨天的情况
            if (endTotalMinutes <= startTotalMinutes) {
                // 跨天：例如22:00-06:00
                return currentTotalMinutes >= startTotalMinutes || currentTotalMinutes <= endTotalMinutes;
            } else {
                // 同一天：例如09:00-18:00
                return currentTotalMinutes >= startTotalMinutes && currentTotalMinutes <= endTotalMinutes;
            }

        } catch (Exception e) {
            log.warn("Failed to parse reception period: {} - {}, defaulting to true", startTime, endTime, e);
            return true;
        }
    }

    /**
     * 检查接收时间类型是否匹配
     */
    private boolean isValidReceptionTime(String receptionTime, LocalDateTime now) {
        if (receptionTime == null || "DAILY".equals(receptionTime)) {
            return true;
        }

        int dayOfWeek = now.getDayOfWeek().getValue(); // 1=Monday, 7=Sunday

        switch (receptionTime) {
            case "WORKDAYS":
                return dayOfWeek >= 1 && dayOfWeek <= 5; // Monday to Friday
            case "HOLIDAYS":
                return dayOfWeek == 6 || dayOfWeek == 7; // Saturday and Sunday
            default:
                log.warn("Unknown reception time type: {}, defaulting to true", receptionTime);
                return true;
        }
    }

    /**
     * 创建批量通知
     * 按plan_id分组: 同一计划的多个预警合并为一个通知
     */
    private int createBatchNotification(List<AlertResult> alerts, NotificationType notificationType, LocalDateTime now) {
        if (alerts.isEmpty()) {
            return 0;
        }

        try {
            // 按plan_id分组处理
            Map<Long, List<AlertResult>> alertsByPlan = alerts.stream()
                    .collect(Collectors.groupingBy(AlertResult::getPlanId));

            int createdCount = 0;

            for (Map.Entry<Long, List<AlertResult>> entry : alertsByPlan.entrySet()) {
                Long planId = entry.getKey();
                List<AlertResult> planAlerts = entry.getValue();

                // 获取第一个预警的配置ID和企业ID（同一批次应该是相同的配置）
                Long configurationId = planAlerts.getFirst().getConfigurationId();
                String enterpriseId = planAlerts.getFirst().getEnterpriseId();

                Optional<AlertConfigurationResponseDto> alertConfigOp = alertConfigConsumerService
                        .getActiveConfigurationById(configurationId);
                if (alertConfigOp.isEmpty()) {
                    log.error("not find alert config {}", configurationId);
                    return 0;
                }
                AlertConfigurationResponseDto alertConfigurationResponseDto = alertConfigOp.get();
                ReceptionSettingsDto receptionSettingsDto = alertConfigurationResponseDto.receptionSettings();
                List<ReceptionRulesEngine.RecipientInfo> recipientInfos = receptionRulesEngine
                        .extractRecipients(receptionSettingsDto);
                String receptionSettingsJson = objectMapper.writeValueAsString(receptionSettingsDto);

                // 创建通知队列记录
                AlertNotificationQueue notification = AlertNotificationQueue.builder()
                        .planId(planId)
                        .enterpriseId(enterpriseId)
                        .configurationId(configurationId)
                        .notificationType(notificationType)
                        .status(NotificationStatus.PENDING)
                        .receptionSettings(receptionSettingsJson)
                        .recipients(serializeRecipients(recipientInfos))
                        .scheduledTime(now)
                        .createdAt(now)
                        .createdBy("system")
                        .build();

                // 保存通知队列
                notification = notificationQueueRepository.save(notification);

                // 关联所有预警到这个通知队列
                for (AlertResult alert : planAlerts) {
                    alert.setAlertNotificationQueueId(notification.getId());
                }
                alertResultRepository.saveAll(planAlerts);

                createdCount++;
                log.debug("Created batch notification {} for {} alerts in plan {}",
                        notification.getId(), planAlerts.size(), planId);
            }

            return createdCount;

        } catch (Exception e) {
            log.error("Failed to create batch notification for {} alerts", alerts.size(), e);
            return 0;
        }
    }

    /**
     * Schedule new notifications for recent alerts
     * 根据预警接收配置的接收时间、时间间隔、接收时段、信息补推、无预警通知等配置进行创建提醒
     */
    private int scheduleNewNotifications(LocalDateTime now) {
        log.info("Starting to schedule new notifications based on reception settings");

        int scheduledCount = 0;

        try {
            // 获取所有活跃的预警配置
            List<AlertConfigurationResponseDto> activeConfigurations = alertConfigConsumerService
                    .getAllActiveConfigurations();
            
            if (activeConfigurations.isEmpty()) {
                log.debug("No active configurations found");
                return 0;
            }
            for (AlertConfigurationResponseDto configuration : activeConfigurations) {
                try {
                    scheduledCount += processConfigurationNotifications(configuration, now);
                    log.debug("Scheduled {} notifications for configuration {}", scheduledCount, configuration.id());
                } catch (Exception e) {
                    log.error("Failed to process notifications for configuration {}",
                            configuration.id(), e);
                }
            }

            log.info("Completed scheduling new notifications. Total scheduled: {}", scheduledCount);
            return scheduledCount;

        } catch (Exception e) {
            log.error("Failed to schedule new notifications", e);
            return scheduledCount;
        }
    }

    /**
     * 处理单个配置的通知调度
     */
    private int processConfigurationNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
        int count = 0;
        ReceptionSettingsDto receptionSettings = configuration.receptionSettings();

        if (receptionSettings == null) {
            log.debug("No reception settings for configuration {}", configuration.id());
            return 0;
        }

        // 1. 处理预警通知（已整合常规预警和信息补推）
        count += scheduleAlertNotifications(configuration, now);

        // 2. 处理无预警通知
        if (Boolean.TRUE.equals(receptionSettings.noAlertNotification())) {
            count += dealNoAlertNotifications(configuration, now);
        }

        return count;
    }

    /**
     * 处理预警通知调度（整合常规预警和信息补推）
     * 根据接收时间、时间间隔、接收时段配置进行调度
     *
     * 信息补推触发条件：
     * 1. 开启信息补推开关（infoPush=true）
     * 2. 当前时间是接收时段的开始时间（开始时间后5分钟内）
     * 3. 满足配置的时间间隔要求
     *
     * 常规预警触发条件：
     * 1. 在接收时段内
     * 2. 满足配置的时间间隔要求
     */
    private int scheduleAlertNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
        int count = 0;
        ReceptionSettingsDto receptionSettings = configuration.receptionSettings();

        // 检查当前时间是否在接收时段内
        if (!isWithinReceptionPeriod(receptionSettings, now)) {
            log.debug("Current time {} is outside reception period for configuration {}",
                    now, configuration.id());
            return 0;
        }

        // 检查接收时间类型（DAILY, WORKDAYS, HOLIDAYS）
        if (!isValidReceptionTime(receptionSettings.receptionTime(), now)) {
            log.debug("Current time {} does not match reception time {} for configuration {}",
                    now, receptionSettings.receptionTime(), configuration.id());
            return 0;
        }

        // 获取最后一次成功的预警通知时间
        LocalDateTime lastNotificationTime = getLastSuccessfulAlertNotificationTime(configuration.id());

        // 检查是否满足通知间隔要求
        if (!satisfiesNotificationInterval(lastNotificationTime, receptionSettings.alertInterval(), now)) {
            log.debug("Notification interval not satisfied for configuration {}, last: {}, interval: {} minutes",
                    configuration.id(), lastNotificationTime, receptionSettings.alertInterval());
            return 0;
        }

        // 判断是否应该触发信息补推
        boolean shouldTriggerInfoPush = Boolean.TRUE.equals(receptionSettings.infoPush())
                && isAtReceptionPeriodStart(receptionSettings, now);

        // 计算查询时间范围和通知类型
        LocalDateTime intervalStart;
        NotificationType notificationType;

        if (shouldTriggerInfoPush) {
            // 信息补推：从最后一次通知时间开始查找所有预警
            intervalStart = calculateAlertTimeRangeStart(configuration, receptionSettings, now);
            notificationType = NotificationType.INFO_PUSH;
            log.debug("Triggering info push for configuration {} from {} to {}",
                    configuration.id(), intervalStart, now);
        } else {
            // 常规预警：使用标准间隔查找预警
            intervalStart = now.minusMinutes(receptionSettings.alertInterval());
            notificationType = NotificationType.ALERT;
            log.debug("Triggering regular alert for configuration {} from {} to {}",
                    configuration.id(), intervalStart, now);
        }

        // 查找该配置下需要通知的预警
        List<AlertResult> pendingAlerts = alertResultRepository
                .findUnnotifiedAlertsByConfigurationAndTimeRange(
                        configuration.id(), intervalStart, now);

        if (!pendingAlerts.isEmpty()) {
            count += createBatchNotification(pendingAlerts, notificationType, now);
            log.debug("Scheduled {} {} notifications for configuration {}",
                    count, notificationType, configuration.id());
        }

        return count;
    }

    /**
     * 判断当前时间是否在接收时段的开始时间附近
     * 用于控制信息补推的触发时机
     */
    private boolean isAtReceptionPeriodStart(ReceptionSettingsDto receptionSettings, LocalDateTime currentTime) {
        if (receptionSettings.receptionPeriod() == null) {
            return false; // 没有设置接收时段，不触发信息补推
        }

        try {
            LocalTime startTime = LocalTime.parse(receptionSettings.receptionPeriod().start());
            LocalTime currentTimeOfDay = currentTime.toLocalTime();

            // 检查当前时间是否在接收时段开始的5分钟内
            LocalTime startWindow = startTime;
            LocalTime endWindow = startTime.plusMinutes(5);

            // 处理跨天的情况
            if (endWindow.isBefore(startWindow)) {
                // 跨天情况：如23:58开始，窗口到00:03
                return currentTimeOfDay.isAfter(startWindow) || currentTimeOfDay.isBefore(endWindow);
            } else {
                // 正常情况：如08:00开始，窗口到08:05
                return !currentTimeOfDay.isBefore(startWindow) && !currentTimeOfDay.isAfter(endWindow);
            }
        } catch (Exception e) {
            log.warn("Failed to parse reception period start time: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否满足通知间隔要求
     */
    private boolean satisfiesNotificationInterval(LocalDateTime lastNotificationTime,
            int intervalMinutes,
            LocalDateTime currentTime) {
        if (lastNotificationTime == null) {
            return true; // 没有历史记录，可以发送
        }

        LocalDateTime nextAllowedTime = lastNotificationTime.plusMinutes(intervalMinutes);
        return !currentTime.isBefore(nextAllowedTime);
    }

    /**
     * 计算预警查询的起始时间
     * 基于最后一次成功的预警通知时间，避免复杂的时间计算
     */
    private LocalDateTime calculateAlertTimeRangeStart(AlertConfigurationResponseDto configuration,
            ReceptionSettingsDto receptionSettings,
            LocalDateTime now) {
        // 获取最后一次成功的预警通知时间
        LocalDateTime lastNotificationTime = getLastSuccessfulAlertNotificationTime(configuration.id());

        if (lastNotificationTime != null) {
            // 基于实际通知历史计算
            LocalDateTime calculatedStart = lastNotificationTime;

            // 限制最大查询范围（避免长时间未运行导致的大量数据查询）
            LocalDateTime maxRangeStart = now.minusDays(7);
            if (calculatedStart.isBefore(maxRangeStart)) {
                calculatedStart = maxRangeStart;
                log.debug("Limited query range to 7 days for configuration {}", configuration.id());
            }

            return calculatedStart;
        } else {
            // 首次运行或没有历史记录，使用默认间隔
            LocalDateTime defaultStart = now.minusMinutes(receptionSettings.alertInterval());
            log.debug("No previous notification found, using default interval for configuration {}",
                    configuration.id());
            return defaultStart;
        }
    }

    /**
     * 处理无预警通知调度
     * 开启后，前一日21:00至当日21:00无预警信息时会下发提示
     */
    private int dealNoAlertNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
        // 检查是否到了21:00点
        if (now.getHour() != 21 || now.getMinute() != 0) {
            return 0;
        }

        // 检查前一日21:00至当日21:00是否有预警
        LocalDateTime yesterday21 = now.minusDays(1).withHour(21).withMinute(0).withSecond(0);
        LocalDateTime today21 = now.withHour(21).withMinute(0).withSecond(0);

        long alertCount = alertResultRepository
                .countAlertsByConfigurationAndTimeRange(
                        configuration.id(), yesterday21, today21);

        if (alertCount == 0) {
            // 创建无预警通知
            scheduleNoAlertNotifications(configuration, now);
            return 1;
        }

        return 0;
    }

    /**
     * Serialize recipients to JSON
     */
    private String serializeRecipients(List<ReceptionRulesEngine.RecipientInfo> recipients) {
        try {
            return objectMapper.writeValueAsString(recipients);
        } catch (Exception e) {
            log.error("Failed to serialize recipients", e);
            return "[]";
        }
    }

    /**
     * Deserialize recipients from JSON
     */
    private List<ReceptionRulesEngine.RecipientInfo> deserializeRecipients(String recipientsJson) {
        try {
            return objectMapper.readValue(recipientsJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class,
                            ReceptionRulesEngine.RecipientInfo.class));
        } catch (Exception e) {
            log.error("Failed to deserialize recipients", e);
            return new ArrayList<>();
        }
    }

    /**
     * Serialize reception settings to JSON
     */
    private String serializeReceptionSettings(Object receptionSettings) {
        try {
            return objectMapper.writeValueAsString(receptionSettings);
        } catch (Exception e) {
            log.error("Failed to serialize reception settings", e);
            return "{}";
        }
    }

    /**
     * Create no-alert SMS message
     */
    private String createNoAlertSmsMessage(PlanDTO planDTO, ReceptionRulesEngine.TimeRangeResult timeRange) {
        return String.format(smsNoWarnContentTemplate, planDTO.name(), timeRange.formattedRange());
    }

    /**
     * Create no-alert email subject
     */
    private String createNoAlertEmailSubject(PlanDTO planDTO, ReceptionRulesEngine.TimeRangeResult timeRange) {
        return String.format(emailNoWarnSubjectTemplate, planDTO.name(), timeRange.formattedRange());
    }

}
