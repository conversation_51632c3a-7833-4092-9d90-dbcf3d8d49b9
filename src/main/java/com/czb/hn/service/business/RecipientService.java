package com.czb.hn.service.business;

import com.czb.hn.dto.recipients.*;
import com.czb.hn.enums.PushSystemType;
import com.czb.hn.enums.PushType;

import java.util.List;

public interface RecipientService {

    /**
     * 获取接收人列表
     * @param requireDto
     * @return 接收人列表
     */
    List<RecipientsResponseDto> getRecipientsList(RecipientsRequireDto requireDto);

    /**
     * 新增接收人列表
     * @param createDto
     * @return 执行结果
     */
    Boolean createRecipients(RecipientsCreateDto createDto);

    /**
     * 删除接收人列表
     * @param id
     * @return 执行结果
     */
    Boolean deleteRecipients(Long id);


    /**
     * 接收人停启用
     * @param id
     * @return
     */
    Boolean enable(Long id, boolean enable);


    /**
     * 获取接收人列表
     * @param planId
     * @param systemType @PushSystemType
     * @param pushType
     * @return 接收人列表
     */
    List<RecipientsBulletinDto> getRecipientsBulletinList(Long planId, PushSystemType systemType, PushType pushType);


    /**
     * 获取接收人信息
     * @param planId
     * @param systemType
     * @param pushType
     * @return 接收人信息
     */
    ReceiveInfoBulletinDto getReceiveInfoBulletin(Long planId, PushSystemType systemType, PushType pushType);

}
