package com.czb.hn.service.cleaner.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.czb.hn.config.ScheduleConfig;
import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.jpa.securadar.entity.*;
import com.czb.hn.jpa.securadar.repository.SinaNewsDwdRepository;
import com.czb.hn.jpa.securadar.repository.SinaNewsOdsRepository;
import com.czb.hn.jpa.securadar.repository.elasticsearch.SinaNewsElasticsearchRepository;
import com.czb.hn.service.cleaner.SinaNewsCleanerService;
import com.czb.hn.service.essync.SinaNewsEsSnycService;
import com.czb.hn.util.DateTimeUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 新浪舆情数据清洗服务实现
 */
@Service
public class SinaNewsCleanerServiceImpl implements SinaNewsCleanerService {

    private static final Logger logger = LoggerFactory.getLogger(SinaNewsCleanerServiceImpl.class);

    // URL正则表达式模式
    private static final Pattern URL_PATTERN = Pattern.compile("https?://[^\\s]+");

    // 表情符号正则表达式模式
    private static final Pattern EMOJI_PATTERN = Pattern.compile(
            "[\\uD83C-\\uDBFF\\uDC00-\\uDFFF]+" // Unicode range for emojis
    );

    // HTML标签正则表达式模式
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");

    // 特殊字符正则表达式模式
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile("[\\\\/:*?\"<>|]");

    // HTML实体正则表达式模式
    private static final Pattern HTML_ENTITY_PATTERN = Pattern
            .compile("&[a-zA-Z][a-zA-Z0-9]*;|&#[0-9]+;|&#x[0-9a-fA-F]+;");

    @Autowired
    private SinaNewsOdsRepository odsRepository;

    @Autowired
    private SinaNewsDwdRepository dwdRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SinaNewsEsSnycService elasticsearchService;

    @Autowired
    private ScheduleConfig scheduleConfig;

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    @Autowired
    private SinaNewsElasticsearchRepository elasticsearchRepository;

    @Value("${sina.data.process.batch-size:100}")
    private int batchSize;

    @Override
    public SinaNewsDwdEntity processRecord(SinaNewsOdsEntity source) {
        if (source == null) {
            return null;
        }

        try {
            // 检查是否已经处理过该记录
            Optional<SinaNewsDwdEntity> existingRecord = dwdRepository.findByContentId(source.getContentId());
            if (existingRecord.isPresent()) {
                logger.debug("Record already processed: {}", source.getContentId());
                return existingRecord.get();
            }

            // 创建并填充DWD实体
            SinaNewsDwdEntity dwdEntity = new SinaNewsDwdEntity();

            // 基本信息转换
            dwdEntity.setContentId(source.getContentId());
            dwdEntity.setTextId(source.getTextId());
            dwdEntity.setTitle(ensureEncodingSafety(source.getTitle())); // 只做编码安全处理
            dwdEntity.setContent(ensureEncodingSafety(source.getText())); // 只做编码安全处理，保留原始内容
            dwdEntity.setSummary(ensureEncodingSafety(source.getSummary())); // 只做编码安全处理
            dwdEntity.setUrl(source.getUrl());

            // 处理短链接
            processShortUrls(source, dwdEntity);

            // 来源信息处理
            dwdEntity.setSource(source.getSource());
            dwdEntity.setSourceWebsite(normalizeSourceWebsite(source.getSourceWebsite()));
            dwdEntity.setCaptureWebsite(source.getCaptureWebsite());
            dwdEntity.setMediaType(source.getOriginType()); // 直接使用枚举类型
            dwdEntity.setMediaTypeSecond(source.getOriginTypeSecond()); // 直接使用枚举类型

//            originTypeThird
            dwdEntity.setMediaTypeThird(source.getOriginTypeThird());
            dwdEntity.setMediaLevel(source.getInfoLevel());
            dwdEntity.setColumn(source.getColumn());

            dwdEntity.setInfoCategory(source.getInfoCategory());
            dwdEntity.setInfoLevel(source.getInfoLevel());
            dwdEntity.setAreaInvolved(source.getAreaInvolved());

            // 时间信息处理
            dwdEntity.setPublishTime(source.getPublishTime());
            dwdEntity.setCaptureTime(source.getCaptureTime());
            dwdEntity.setProcessTime(LocalDateTime.now());

            // 作者信息处理
            processAuthorInfo(source, dwdEntity);

            // 地理位置信息处理
            processLocationInfo(source, dwdEntity);

            // 备案信息处理
            processFilingInfo(source, dwdEntity);

            // 分类信息处理
            dwdEntity.setSecondTrades(processSecondTrades(source.getSecondTrades()));

            // 情感分析处理
            dwdEntity.setEmotion(source.getEmotion()); // 直接使用枚举类型
            dwdEntity.setSensitivityType(source.getSensitivityType()); // 直接使用枚举类型
            dwdEntity.setSensitivityScore(source.getSensitivityScore());

            // 特征提取
            dwdEntity.setContentTypes(source.getContentTypes());
            dwdEntity.setAnnotations(processAnnotations(source));

            // 互动数据处理
            processInteractionData(source, dwdEntity);

            // 相似性信息处理
            dwdEntity.setSimilarityNum(getSimilarityNum(source.getSimilarityTag()));
            dwdEntity.setSimilarityTag(source.getSimilarityTag());

            // 转发信息处理
            processForwardInfo(source, dwdEntity);

            // 匹配信息处理
            dwdEntity.setMatchType(source.getMatchType()); // 直接使用枚举类型
            dwdEntity.setMatchInfo(source.getMatchInfo());
            dwdEntity.setMatchTicket(source.getMatchTicket());
            dwdEntity.setMatchName(source.getMatchName());

            // 媒体分类
            dwdEntity.setResultView(source.getResultView()); // 直接使用枚举类型

            // 图片和视频信息处理
            processMediaInfo(source, dwdEntity);

            // 源记录关联
            dwdEntity.setOdsId(source.getId());
            dwdEntity.setProcessedToDws(false);

            // NER信息处理
            dwdEntity.setNerInfoExt(processNerInfo(source));

            // 保存DWD记录
            SinaNewsDwdEntity savedEntity = dwdRepository.save(dwdEntity);

            // 更新ODS记录处理状态
            source.setProcessed(true);
            odsRepository.save(source);

            // 实时同步到Elasticsearch
            try {
                elasticsearchService.syncToElasticsearch(savedEntity);
                logger.info("Successfully synced record to Elasticsearch: {}", source.getContentId());
            } catch (Exception e) {
                // 同步到ES失败不影响主流程
                logger.error("Error syncing record to Elasticsearch: {}", e.getMessage(), e);
            }

            logger.info("Successfully processed record: {}", source.getContentId());

            return savedEntity;

        } catch (Exception e) {
            logger.error("Error processing record {}: {}", source.getContentId(), e.getMessage(), e);
            throw new RuntimeException("Error processing record", e);
        }
    }

    /**
     * 处理短链接
     */
    private void processShortUrls(SinaNewsOdsEntity source, SinaNewsDwdEntity target) {
        try {
            if (source.getShortUrl() != null) {
                target.setShortUrl(source.getShortUrl());
            }
        } catch (Exception e) {
            logger.warn("Error processing short URLs: {}", e.getMessage());
        }
    }

    /**
     * 处理作者信息
     */
    private void processAuthorInfo(SinaNewsOdsEntity source, SinaNewsDwdEntity target) {
        target.setAuthor(source.getAuthor());
        target.setAuthorId(source.getUserAccount());
        target.setAuthorGender(source.getUserGender());
        target.setAuthorProfileUrl(source.getUserProfileImage());
        target.setAuthorHomePage(source.getHomePage());
        target.setAuthorProvince(source.getUserProvince());
        target.setAuthorCity(source.getUserCity());
        target.setAuthorVerified(source.getUserVerified());
        target.setAuthorVerifiedType(source.getUserVerifiedType()); // 直接使用枚举类型
        target.setAuthorTags(source.getTags());
        target.setAuthorContentCount(source.getContentsCount());
        target.setAuthorFollowingCount(source.getFriendsCount());
        target.setAuthorFollowersCount(source.getFollowersCount());
        // 作者创建时间可能需要格式转换
        if (source.getCreateTime() != null) {
            target.setAuthorCreateTime(DateTimeUtil.parseDateTime(source.getCreateTime()));
        }
    }

    /**
     * 处理地理位置信息
     */
    private void processLocationInfo(SinaNewsOdsEntity source, SinaNewsDwdEntity target) {
        target.setPublishProvince(source.getPublishProvince());
        target.setPublishCity(source.getPublishCity());
        target.setContentProvince(source.getContentProvince());
        target.setContentCity(source.getContentCity());
        target.setLocation(source.getLocation());
    }

    /**
     * 处理备案信息
     */
    private void processFilingInfo(SinaNewsOdsEntity source, SinaNewsDwdEntity target) {
        target.setFilingProvince(source.getFilingProvince());
        target.setWebFilingNumber(source.getWebFilingNumber());
        target.setWebFilingUnits(source.getWebFilingUnits());
    }

    /**
     * 处理互动数据
     */
    private void processInteractionData(SinaNewsOdsEntity source, SinaNewsDwdEntity target) {
        Long commentNum = source.getCommentNum() != null ? source.getCommentNum() : 0L;
        Long forwardNum = source.getForwardNum() != null ? source.getForwardNum() : 0L;
        Long praiseNum = source.getPraiseNum() != null ? source.getPraiseNum() : 0L;
        Long shareNum = source.getShareNum() != null ? source.getShareNum() : 0L;
        Long collectionNum = source.getCollectionNum() != null ? source.getCollectionNum() : 0L;
        Long answerNum = source.getAnswerNum() != null ? source.getAnswerNum() : 0L;
        Long lookingNum = source.getLookingNum() != null ? source.getLookingNum() : 0L;

        target.setCommentCount(commentNum);
        target.setForwardCount(forwardNum);
        target.setPraiseCount(praiseNum);
        target.setShareCount(shareNum);
        target.setCollectionCount(collectionNum);
        target.setAnswerCount(answerNum);
        target.setLookingCount(lookingNum);

        // 计算总互动量
        target.setInteractionCount(commentNum + forwardNum + praiseNum + shareNum +
                collectionNum + answerNum + lookingNum);
    }

    /**
     * 处理转发信息
     */
    private void processForwardInfo(SinaNewsOdsEntity source, SinaNewsDwdEntity target) {
        target.setIsOriginal(source.getIsOriginal()); // 直接使用枚举类型
        target.setIsForward(source.getRootContentId() != null && !source.getRootContentId().isEmpty());
        target.setWbForwardType(source.getWbForwardType());
        target.setRootContentId(source.getRootContentId());
        target.setCommentText(source.getCommentText());
        target.setCommentForwardText(source.getCommentForwardText());
    }

    /**
     * 处理媒体信息
     */
    private void processMediaInfo(SinaNewsOdsEntity source, SinaNewsDwdEntity target) {
        try {
            // 处理图片信息
            if (source.getImageUrls() != null) {
                target.setImages(source.getImageUrls());
            }

            // 处理OCR内容
            if (source.getOcrContents() != null) {
                target.setOcrContents(source.getOcrContents());
            }

            // 处理视频信息
            if (source.getVideoUrl() != null) {
                target.setVideoUrl(source.getVideoUrl());
                target.setVideoCoverUrl(source.getVideoCoverUrl());
            }

        } catch (Exception e) {
            logger.warn("Error processing media info: {}", e.getMessage());
        }
    }

    /**
     * 处理注解信息
     */
    private String processAnnotations(SinaNewsOdsEntity source) {
        try {
            if (source.getAnnotations() != null) {
                return source.getAnnotations();
            }
        } catch (Exception e) {
            logger.warn("Error processing annotations: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 处理NER信息
     */
    private String processNerInfo(SinaNewsOdsEntity source) {
        try {
            if (source.getNerInfoExt() != null) {
                return source.getNerInfoExt();
            }
        } catch (Exception e) {
            logger.warn("Error processing NER info: {}", e.getMessage());
        }
        return null;
    }

    @Override
    @Scheduled(cron = "#{@scheduleConfig.cleaner.enabled ? @scheduleConfig.cleaner.cron : '-'}")
    public void scheduledProcessing() {
        // 检查是否启用定时任务
        if (!scheduleConfig.getCleaner().isEnabled()) {
            logger.debug("Data cleaner scheduled task is disabled, skipping execution");
            return;
        }

        try {
            logger.info("Starting scheduled data cleaning (Environment: {})",
                    scheduleConfig.getCleaner().getEnvironment());

            int processedCount = processAllUnprocessedRecords();
            logger.info("Scheduled data cleaning completed. Environment: {}, Processed: {} records",
                    scheduleConfig.getCleaner().getEnvironment(), processedCount);
        } catch (Exception e) {
            logger.error("Error in scheduled data cleaning (Environment: {}): {}",
                    scheduleConfig.getCleaner().getEnvironment(), e.getMessage(), e);
        }
    }

    @Override
    public int processAllUnprocessedRecords() {
        int processedCount = 0;
        int totalCount = (int) odsRepository.countUnprocessedRecords();

        if (totalCount == 0) {
            logger.info("No unprocessed records found");
            return 0;
        }

        logger.info("Found {} unprocessed records", totalCount);

        List<SinaNewsOdsEntity> unprocessedRecords = odsRepository.findUnprocessedRecords();
        for (SinaNewsOdsEntity record : unprocessedRecords) {
            try {
                processRecord(record);
                processedCount++;

                if (processedCount % 100 == 0) {
                    logger.info("Processed {}/{} records", processedCount, totalCount);
                }
            } catch (Exception e) {
                logger.error("Error processing record {}: {}", record.getContentId(), e.getMessage());
            }
        }

        logger.info("Completed processing {}/{} records", processedCount, totalCount);
        return processedCount;
    }

    @Override
    public List<SinaNewsDwdEntity> findByDate(LocalDate date) {
        if (date == null) {
            return new ArrayList<>();
        }

        return dwdRepository.findByPublishDate(date);
    }

    @Override
    public SinaNewsDwdEntity findByContentId(String contentId) {
        if (contentId == null || contentId.isBlank()) {
            return null;
        }

        return dwdRepository.findByContentId(contentId).orElse(null);
    }


    /* 辅助方法 */

    /**
     * 清理文本，去除HTML标签、URL、表情符号等
     * 按照数据仓库清洗标准，确保文本内容的纯净性和编码安全性
     * 使用Jsoup进行强化的HTML清理，特别适用于复杂的表格结构
     */
    private String cleanText(String text) {
        if (text == null || text.isBlank()) {
            return "";
        }

        String cleanedText = text;

        try {
            // 1. 先去除HTML标签
            cleanedText = HTML_TAG_PATTERN.matcher(cleanedText).replaceAll(" ");

            // 2. 处理HTML实体，保留文本内容
            cleanedText = cleanedText.replace("&nbsp;", " ")
                    .replace("&amp;", "&")
                    .replace("&lt;", " ") // 替换为空格，保留周围的文本
                    .replace("&gt;", " ") // 替换为空格，保留周围的文本
                    .replace("&quot;", "\"")
                    .replace("&#39;", "'");

            // 3. 处理其他可能的HTML实体
            cleanedText = HTML_ENTITY_PATTERN.matcher(cleanedText).replaceAll(" ");

            // 3. 去除URL（可选：根据业务需求决定是否保留）
            cleanedText = URL_PATTERN.matcher(cleanedText).replaceAll(" ");

            // 4. 去除表情符号（可选：根据业务需求决定是否保留）
            cleanedText = EMOJI_PATTERN.matcher(cleanedText).replaceAll("");

            // 5. 去除特殊控制字符，保留中文、英文、数字和常用标点
            cleanedText = cleanedText.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

            // 6. 标准化空白字符（空格、制表符、换行符等）
            cleanedText = cleanedText.replaceAll("\\s+", " ").trim();

            // 7. 确保字符编码安全性
            cleanedText = ensureEncodingSafety(cleanedText);

            // 8. 确保字符串不为空
            if (cleanedText.isBlank()) {
                return "";
            }

            return cleanedText;

        } catch (Exception e) {
            // 如果清洗过程出现异常，记录日志并返回原始文本的安全版本
            logger.warn("Error cleaning text, returning safe version: {}", e.getMessage());
            return ensureEncodingSafety(text.replaceAll("\\s+", " ").trim());
        }
    }


    /**
     * 确保字符编码安全性，专注于解决数据库存储问题
     * 保留所有内容，只处理真正导致存储错误的字符
     */
    private String ensureEncodingSafety(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        try {
            // 只移除真正导致MySQL编码问题的控制字符
            // 保留所有可见字符，包括HTML标签、中文、表情符号等
            String result = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", " ");

            // 确保UTF-8编码安全性
            byte[] utf8Bytes = result.getBytes("UTF-8");
            String utf8String = new String(utf8Bytes, "UTF-8");

            return utf8String;

        } catch (Exception e) {
            logger.warn("Error ensuring encoding safety: {}, returning original text", e.getMessage());
            // 如果处理失败，返回原始文本
            return text;
        }
    }

    /**
     * 标准化来源网站名称
     */
    private String normalizeSourceWebsite(String sourceWebsite) {
        if (sourceWebsite == null || sourceWebsite.isBlank()) {
            return "未知来源";
        }

        return sourceWebsite;
    }

    /**
     * 处理二级行业分类
     */
    private String processSecondTrades(String secondTradesJson) {
        try {
            if (secondTradesJson == null || secondTradesJson.isBlank()) {
                return "其他";
            }

            List<String> trades = objectMapper.readValue(secondTradesJson, List.class);

            if (trades == null || trades.isEmpty()) {
                return "其他";
            }

            return String.join(",", trades);

        } catch (Exception e) {
            logger.warn("Error parsing secondTrades: {}", e.getMessage());
            return "其他";
        }
    }

    /**
     * 处理相似文章数量
     */
    private Long getSimilarityNum(String similarityTag) {
        try {
            logger.info("同步相似文章数: {}", similarityTag);

            BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
            boolQueryBuilder.filter(fq -> fq
                    .term(t -> t
                            .field("similarityTag")
                            .value(similarityTag)));

            SearchRequest searchRequest = SearchRequest.of(builder -> {
                builder.index("sina_news")
                        .query(boolQueryBuilder.build()._toQuery());
                return builder;
            });

            SearchResponse<SinaNewsDocument> response = elasticsearchClient.search(searchRequest, SinaNewsDocument.class);

            List<SinaNewsDocument> hits = response.hits().hits().stream().map(Hit::source).toList();

            Long similarityNum = (long) hits.size() + 1;

            for (SinaNewsDocument hit : hits) {
                hit.setSimilarityNum(similarityNum);
                elasticsearchRepository.save(hit);
            }

            logger.info("同步完成，相似文章数量: {}", similarityNum);
            return similarityNum;
        } catch (Exception e) {
            logger.error("Error in getSimilarityNum: {}", e.getMessage(), e);
            return 0L;
        }
    }

}