package com.czb.hn.service.bulletin.impl;

import com.czb.hn.dto.briefing.config.ContentSettingsDto;
import com.czb.hn.dto.response.briefing.*;
import com.czb.hn.dto.response.search.SearchRequestDto;
import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;
import com.czb.hn.jpa.securadar.entity.BulletinContentDataEntity;
import com.czb.hn.jpa.securadar.repository.BulletinContentDataRepository;
import com.czb.hn.service.bulletin.BulletinContentDataService;
import com.czb.hn.service.business.ElasticsearchBriefingService;
import com.czb.hn.service.business.ElasticsearchSearchService;
import com.czb.hn.util.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 简报内容数据服务实现类
 */
@Service
public class BulletinContentDataServiceImpl implements BulletinContentDataService {

    private static final Logger log = LoggerFactory.getLogger(BulletinContentDataServiceImpl.class);

    private final BulletinContentDataRepository bulletinContentDataRepository;

    private final ElasticsearchBriefingService elasticsearchBriefingService;
    private final ElasticsearchSearchService elasticsearchSearchService;


    private final ObjectMapper objectMapper;

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public BulletinContentDataServiceImpl(BulletinContentDataRepository bulletinContentDataRepository, ElasticsearchBriefingService elasticsearchBriefingService, ElasticsearchSearchService elasticsearchSearchService, ObjectMapper objectMapper) {
        this.bulletinContentDataRepository = bulletinContentDataRepository;
        this.elasticsearchBriefingService = elasticsearchBriefingService;
        this.elasticsearchSearchService = elasticsearchSearchService;
        this.objectMapper = objectMapper;
    }

    @Override
    @Transactional
    public BulletinContentDataEntity generateAndSaveBulletinContentData(
            Long generationId,
            Long planId,
            String startTime,
            String endTime,
            String paramsJson) {

        Assert.notNull(generationId, "生成记录ID不能为空");
        Assert.notNull(planId, "方案ID不能为空");
        Assert.hasText(startTime, "开始时间不能为空");
        Assert.hasText(endTime, "结束时间不能为空");
        Assert.hasText(paramsJson, "任务参数不能为空");

        try {
            log.info("开始生成简报内容数据，生成记录ID: {}, 任务ID: {}, 时间范围: {} 至 {}",
                    generationId, planId, startTime, endTime);
            // 解析外层参数
            JsonNode root = objectMapper.readTree(paramsJson);

            // 1. 解析外层字段
            String bulletinTitle = root.path("bulletinTitle").asText();
            List<String> emailReceivers = new ArrayList<>();
            if (root.has("emailReceivers") && root.get("emailReceivers").isArray()) {
                for (JsonNode node : root.get("emailReceivers")) {
                    emailReceivers.add(node.asText());
                }
            }
            List<String> smsReceivers = new ArrayList<>();
            if (root.has("smsReceivers") && root.get("smsReceivers").isArray()) {
                for (JsonNode node : root.get("smsReceivers")) {
                    smsReceivers.add(node.asText());
                }
            }
            int dataRange = root.path("dataRange").asInt(1);

            // 2. 解析内层 params 字段为 ContentSettingsDto
            ContentSettingsDto contentSettings = null;
            JsonNode paramsNode = root.path("params");
            log.info("paramsNode: {}", paramsNode);
            if (!paramsNode.isMissingNode() && !paramsNode.isNull() && !paramsNode.asText().isBlank()) {
                contentSettings = objectMapper.readValue(paramsNode.asText(), ContentSettingsDto.class);
            }
            log.info("contentSettings: {}", JsonUtil.toJson(contentSettings));
            // 3. 提取筛选条件参数
            List<Integer> contentTypes = contentSettings != null ? contentSettings.contentTypes() : List.of();
            List<Integer> sensitivityType = contentSettings != null ? contentSettings.sensitivityTypes(): List.of();
            List<Integer> isOriginal = contentSettings != null ? contentSettings.isOriginal() : List.of();
            List<String> secondTrades = contentSettings != null ? contentSettings.secondTrades() : List.of();
            List<String> mediaType = contentSettings != null ? contentSettings.mediaTypes() : List.of();
            List<String> mediaLevel = contentSettings != null ? contentSettings.mediaLevels() : List.of();
            Integer matchMethod = contentSettings != null ? contentSettings.matchMethod() : null;

            startTime = "2025-01-01 00:00:00";
            // 1. 查询ES获取简报内容数据
            // 获取敏感信息趋势图
            HistogramSummeryDto histogramSummery = elasticsearchBriefingService.getSensitiveInfoTrend(
                    startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
            log.info("敏感信息趋势图参数： startTime:{}, end:{}, planId:{},sensitivityType:{},mediaType:{}, contentTypes:{},isOriginal:{},secondTrades:{},mediaLevel:{}", startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
            log.info("敏感信息趋势图数据：{}", JsonUtil.toJson(histogramSummery));
            // === 新增：聚合趋势图数据 ===
            // 1. 获取三条线
            List<HistogramDto> sensitiveList = histogramSummery.getSensitiveInfoTrend();
            List<HistogramDto> neutralList = histogramSummery.getNeutralInfoTrend();
            List<HistogramDto> nonSensitiveList = histogramSummery.getNonSensitiveInfoTrend();

            // 2. 计算分段时间点
            List<LocalDateTime> timePoints = buildTimePoints(startTime, endTime, dataRange);

            // 3. 聚合数据
            List<HistogramGroupDto> histogramGroupList = new ArrayList<>();
            for (LocalDateTime time : timePoints) {
                long sensitive = findCountByTime(sensitiveList, time);
                long neutral = findCountByTime(neutralList, time);
                long nonSensitive = findCountByTime(nonSensitiveList, time);
                histogramGroupList.add(new HistogramGroupDto(time, sensitive, neutral, nonSensitive));
            }

            // 获取媒体来源分布
            List<MediaDistributionDto> mediaDistribution = elasticsearchBriefingService.mediaDistribution(
                    startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
                    log.info("获取媒体来源分布: startTime{}, end:{}, planId:{},sensitivityType:{},mediaType:{}, contentTypes:{},isOriginal:{},secondTrades:{},mediaLevel:{}", startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
                    log.info("媒体来源分布：{}", JsonUtil.toJson(mediaDistribution));

            // 获取媒体参与详情
            List<MediaTierDto> mediaTierData = elasticsearchBriefingService.MediaDetail(
                    startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
                    log.info("获取媒媒体参与详情：startTime:{}, end:{}, planId:{},sensitivityType:{},mediaType:{}, contentTypes:{},isOriginal:{},secondTrades:{},mediaLevel:{}", startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
            log.info("媒体参与详情：{}", JsonUtil.toJson(mediaTierData));
            
            // 获取高频词
            List<HighFrequencyWordDto> highFrequencyWords = elasticsearchBriefingService.HighFrequencyWords(
                    startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
                    log.info("获取高频词：startTime:{}, end:{}, planId:{},sensitivityType:{},mediaType:{}, contentTypes:{},isOriginal:{},secondTrades:{},mediaLevel:{}", startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
            log.info("高频词结果数据：{}", JsonUtil.toJson(highFrequencyWords));

            // 获取情感分布
            List<EmotionDistributionDto> emotionDistribution = elasticsearchBriefingService.EmotionDistribution(
                    startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
                    log.info("获取情感分布：startTime:{}, end:{}, planId:{},sensitivityType:{},mediaType:{}, contentTypes:{},isOriginal:{},secondTrades:{},mediaLevel:{}", startTime, endTime, planId, sensitivityType, mediaType, contentTypes, isOriginal, secondTrades, mediaLevel);
            log.info("情感分布数据：{}", JsonUtil.toJson(emotionDistribution));
            
            // 获取敏感信息导读
            SearchRequestDto searchRequestDto = new SearchRequestDto();
            searchRequestDto.setPlanId(planId);
            searchRequestDto.setStartTime(startTime);
            searchRequestDto.setEndTime(endTime);
            searchRequestDto.setSortRule(3);
            searchRequestDto.setSensitivityType(sensitivityType);
            searchRequestDto.setSimilarityDisplayRule(false);
            searchRequestDto.setMatchMethod(matchMethod);
            searchRequestDto.setMediaTypes(mediaType);
            searchRequestDto.setMediaTypeSecond(secondTrades);
            searchRequestDto.setContentType(contentTypes);
            searchRequestDto.setIsOriginal(isOriginal);
            searchRequestDto.setSecondTrades(secondTrades);
            searchRequestDto.setMediaLevel(mediaLevel);

            List<SinaNewsSearchResponseDto> sensitiveInfoSummary = elasticsearchSearchService.SinaNewsMonitor(searchRequestDto, 10, 1);

            // 2. 构建概述数据
            OverviewDto overviewDto = buildOverviewDto(mediaDistribution, histogramSummery);

            // 3. 创建实体并保存
            LocalDateTime now = LocalDateTime.now();
            BulletinContentDataEntity entity = new BulletinContentDataEntity();
            entity.setGenerationId(generationId);
            entity.setStartTime(LocalDateTime.parse(startTime, TIME_FORMATTER));
            entity.setEndTime(LocalDateTime.parse(endTime, TIME_FORMATTER));
            entity.setOverviewData(JsonUtil.toJson(overviewDto));
            // 替换为聚合后的趋势图数据
            entity.setHistogramData(JsonUtil.toJson(histogramGroupList));
            entity.setMediaDistributionData(JsonUtil.toJson(mediaDistribution));
            entity.setMediaTierData(JsonUtil.toJson(mediaTierData));
            entity.setHighFrequencyWordData(JsonUtil.toJson(highFrequencyWords));
            entity.setEmotionDistributionData(JsonUtil.toJson(emotionDistribution));
            entity.setSensitiveInfoSummaryData(JsonUtil.toJson(sensitiveInfoSummary));
            entity.setCreatedAt(now);
            entity.setUpdatedAt(now);

            BulletinContentDataEntity savedEntity = bulletinContentDataRepository.save(entity);

            log.info("简报内容数据生成并保存完成，生成记录ID: {}", generationId);

            return savedEntity;

        } catch (Exception e) {
            log.error("生成简报内容数据失败，生成记录ID: {}", generationId, e);
            throw new RuntimeException("生成简报内容数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public BriefingSummaryDto getBulletinContentData(Long generationId) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        Optional<BulletinContentDataEntity> entityOpt = bulletinContentDataRepository.findByGenerationId(generationId);
        if (entityOpt.isEmpty()) {
            log.error("未找到简报内容数据，生成记录ID: {}", generationId);
            return null;
        }

        log.info("简报内容数据：{}", JsonUtil.toJson(entityOpt.get()));
        BulletinContentDataEntity entity = entityOpt.get();

        // 构建并返回DTO
        OverviewDto overview = JsonUtil.fromJson(entity.getOverviewData(), OverviewDto.class);
        List<HistogramGroupDto> histogramSummary = JsonUtil.fromJsonList(entity.getHistogramData(), HistogramGroupDto.class);
        List<MediaDistributionDto> mediaDistribution = JsonUtil.fromJsonList(entity.getMediaDistributionData(),
                MediaDistributionDto.class);
        List<MediaTierDto> mediaTier = JsonUtil.fromJsonList(entity.getMediaTierData(), MediaTierDto.class);
        List<HighFrequencyWordDto> highFrequencyWords = JsonUtil.fromJsonList(entity.getHighFrequencyWordData(),
                HighFrequencyWordDto.class);
        List<EmotionDistributionDto> emotionDistribution = JsonUtil.fromJsonList(entity.getEmotionDistributionData(),
                EmotionDistributionDto.class);
        List<SinaNewsSearchResponseDto> sensitiveInfoSummary = JsonUtil
                .fromJsonList(entity.getSensitiveInfoSummaryData(), SinaNewsSearchResponseDto.class);

        return new BriefingSummaryDto(
                entity.getStartTime(),
                entity.getEndTime(),
                overview,
                null,
                histogramSummary,
                mediaDistribution,
                mediaTier.isEmpty() ? null : mediaTier.get(0),
                highFrequencyWords,
                emotionDistribution,
                sensitiveInfoSummary);
    }

    /**
     * 构建监测概述DTO
     */
    private OverviewDto buildOverviewDto(List<MediaDistributionDto> mediaDistribution,
            HistogramSummeryDto histogramSummary) {
        OverviewDto overviewDto = new OverviewDto();

        // 计算总数量
        long totalCount = 0;
        long commentTotal = 0;
        long forwardCount = 0;
        long replyCount = 0;
        long likeCount = 0;
        long readCount = 0;

        // 获取敏感、非敏感、中性信息数量
        if (histogramSummary != null) {
            SensitivityTypeDistributionDto sensitiveData = histogramSummary.getSensitiveInfoDistribution();
            SensitivityTypeDistributionDto nonSensitiveData = histogramSummary.getNonSensitiveInfoDistribution();
            SensitivityTypeDistributionDto neutralData = histogramSummary.getNeutralInfoDistribution();

            // 处理敏感数据
            if (sensitiveData != null && sensitiveData.getCount() != null) {
                overviewDto.setSensitiveTotal(sensitiveData.getCount());
                overviewDto.setSensitiveRatio(sensitiveData.getPercentage());
            }

            // 处理非敏感数据
            if (nonSensitiveData != null && nonSensitiveData.getCount() != null) {
                overviewDto.setNonSensitiveTotal(nonSensitiveData.getCount());
                overviewDto.setNonSensitiveRatio(nonSensitiveData.getPercentage());
            }

            // 处理中性数据
            if (neutralData != null && neutralData.getCount() != null) {
                overviewDto.setNeutralTotal(neutralData.getCount());
                overviewDto.setNeutralRatio(neutralData.getPercentage());
            }

            // 计算总数，处理可能的 null 值
            long sensitiveCount = (sensitiveData != null && sensitiveData.getCount() != null) ? sensitiveData.getCount() : 0L;
            long nonSensitiveCount = (nonSensitiveData != null && nonSensitiveData.getCount() != null) ? nonSensitiveData.getCount() : 0L;
            long neutralCount = (neutralData != null && neutralData.getCount() != null) ? neutralData.getCount() : 0L;

            totalCount = sensitiveCount + nonSensitiveCount + neutralCount;
        }


        overviewDto.setContentTotal(totalCount);
        overviewDto.setCommentTotal(commentTotal);
        overviewDto.setMediaDistribution(mediaDistribution);
        overviewDto.setForwardCount(forwardCount);
        overviewDto.setReplyCount(replyCount);
        overviewDto.setLikeCount(likeCount);
        overviewDto.setReadCount(readCount);

        // 查找主要来源
        if (mediaDistribution != null && !mediaDistribution.isEmpty()) {
            String primarySource = "";
            long maxCount = 0;
            for (MediaDistributionDto distribution : mediaDistribution) {
                if (!"全部".equals(distribution.getMediaName()) &&
                        distribution.getCount() > maxCount) {
                    maxCount = distribution.getCount();
                    primarySource = distribution.getMediaName();
                }
            }
            overviewDto.setPrimarySource(primarySource);
        }

        return overviewDto;
    }

    // 工具方法：时间分段
    /**
     * 根据简报类型和时间范围，生成6个分段时间点
     * @param startTimeStr 开始时间字符串
     * @param endTimeStr 结束时间字符串
     * @param dataRange 数据范围（1=日报，7=周报，30=月报）
     * @return 时间点列表
     */
    private List<LocalDateTime> buildTimePoints(String startTimeStr, String endTimeStr, int dataRange) {
        LocalDateTime start = LocalDateTime.parse(startTimeStr, TIME_FORMATTER);
        LocalDateTime end = LocalDateTime.parse(endTimeStr, TIME_FORMATTER);
        List<LocalDateTime> result = new ArrayList<>();
        if (dataRange == 1) { // 日报，每4小时
            for (int i = 0; i < 6; i++) {
                result.add(start.plusHours(i * 4));
            }
        } else if (dataRange == 7) { // 周报，每天，最后一段2天
            for (int i = 0; i < 5; i++) {
                result.add(start.plusDays(i));
            }
            result.add(start.plusDays(6)); // 第6段为最后两天
        } else if (dataRange == 30) { // 月报，每5天
            for (int i = 0; i < 6; i++) {
                result.add(start.plusDays(i * 5));
            }
        } else { // 默认按日报
            for (int i = 0; i < 6; i++) {
                result.add(start.plusHours(i * 4));
            }
        }
        return result;
    }

    // 工具方法：查找某时间点的count
    private long findCountByTime(List<HistogramDto> list, LocalDateTime time) {
        if (list == null) return 0L;
        for (HistogramDto dto : list) {
            if (dto.getTime().equals(time)) {
                return dto.getCount() == null ? 0L : dto.getCount();
            }
        }
        return 0L;
    }
}