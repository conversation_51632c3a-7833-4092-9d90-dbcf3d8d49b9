package com.czb.hn.service.bulletin.impl;

import com.czb.hn.dto.response.briefing.OverviewDto;
import com.czb.hn.jpa.securadar.entity.BulletinContentDataEntity;
import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;
import com.czb.hn.jpa.securadar.entity.Plan;
import com.czb.hn.jpa.securadar.repository.BulletinContentDataRepository;
import com.czb.hn.jpa.securadar.repository.BulletinGenerationRecordRepository;
import com.czb.hn.jpa.securadar.repository.PlanRepository;
import com.czb.hn.service.bulletin.BulletinEmailService;
import com.czb.hn.service.file.FileStorageService;
import com.czb.hn.util.EmailTemplateUtil;
import com.czb.hn.util.EmailUtil;
import com.czb.hn.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMultipart;
import java.time.format.DateTimeFormatter;

/**
 * 简报邮件服务实现类
 */
@Service
public class BulletinEmailServiceImpl implements BulletinEmailService {
    
    private static final Logger log = LoggerFactory.getLogger(BulletinEmailServiceImpl.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    @Autowired
    private BulletinGenerationRecordRepository generationRecordRepository;
    
    @Autowired
    private BulletinContentDataRepository contentDataRepository;
    
    @Autowired
    private PlanRepository planRepository;
    
    @Autowired
    private FileStorageService fileStorageService;
    
    @Autowired
    private EmailTemplateUtil emailTemplateUtil;
    
    @Autowired
    private EmailUtil emailUtil;
    
    @Override
    public boolean sendBulletinEmail(Long generationId, String email) {
        try {
            // 获取生成记录
            BulletinGenerationRecordEntity record = generationRecordRepository.findById(generationId)
                    .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));
            
            // 获取方案信息
            Plan plan = planRepository.findById(record.getPlanId())
                    .orElseThrow(() -> new IllegalArgumentException("找不到方案信息: " + record.getPlanId()));
            
            // 获取简报内容数据
            BulletinContentDataEntity contentData = contentDataRepository.findByGenerationId(generationId)
                    .orElseThrow(() -> new IllegalArgumentException("找不到简报内容数据: " + generationId));
            
            // 解析概览数据
            OverviewDto overview = JsonUtil.fromJson(contentData.getOverviewData(), OverviewDto.class);
            if (overview == null) {
                throw new IllegalStateException("无法解析简报概览数据");
            }
            
            // 获取文件内容
            byte[] pdfContent = fileStorageService.getFileContent(record.getFileObjectName());
            if (pdfContent == null || pdfContent.length == 0) {
                throw new IllegalStateException("简报PDF文件内容为空");
            }
            
            // 准备邮件参数
            String planName = plan.getName();
            String briefingType = getBriefingTypeDisplay(record.getBulletinType());
            String briefingPeriod = getBriefingPeriod(record);
            String taskTime = DATE_FORMATTER.format(record.getGenerationTime());
            String totalCount = String.valueOf(overview.getContentTotal());
            String sensitiveRatio = String.format("%.2f%%", overview.getSensitiveRatio() * 100);
            String pdfFileName = record.getBulletinTitle() + ".pdf";
            
            // 创建邮件内容
            MimeMultipart emailContent = emailTemplateUtil.createBulletinEmailContent(
                    planName, briefingType, briefingPeriod, taskTime, totalCount, sensitiveRatio, pdfContent, pdfFileName);
            
            // 发送邮件
            String subject = getBulletinEmailSubject(planName, briefingType);
            emailUtil.sendComplexEmail(email, subject, emailContent);
            
            return true;
        } catch (Exception e) {
            log.error("发送简报邮件失败, 生成记录ID: {}, 邮箱: {}", generationId, email, e);
            return false;
        }
    }
    
    @Override
    public String getBulletinEmailSubject(String planName, String briefingType) {
        return String.format("【%s】舆情监控%s已生成", planName, briefingType);
    }
    
    /**
     * 获取简报类型显示名称
     */
    private String getBriefingTypeDisplay(String bulletinType) {
        switch (bulletinType) {
            case "DAILY":
                return "日报";
            case "WEEKLY":
                return "周报";
            case "MONTHLY":
                return "月报";
            default:
                return bulletinType;
        }
    }
    
    /**
     * 获取简报时间段描述
     */
    private String getBriefingPeriod(BulletinGenerationRecordEntity record) {
        return DATE_FORMATTER.format(record.getStartTime()) + " 至 " + DATE_FORMATTER.format(record.getEndTime());
    }
} 