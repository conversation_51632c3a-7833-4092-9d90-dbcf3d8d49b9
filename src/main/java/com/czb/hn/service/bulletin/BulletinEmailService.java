package com.czb.hn.service.bulletin;

import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;

/**
 * 简报邮件服务接口
 */
public interface BulletinEmailService {
    
    /**
     * 发送简报邮件
     * 
     * @param generationId 简报生成记录ID
     * @param email 收件人邮箱
     * @return 是否发送成功
     */
    boolean sendBulletinEmail(Long generationId, String email);
    
    /**
     * 获取简报邮件主题
     * 
     * @param planName 方案名称
     * @param briefingType 简报类型（日报/周报/月报）
     * @return 邮件主题
     */
    String getBulletinEmailSubject(String planName, String briefingType);
} 