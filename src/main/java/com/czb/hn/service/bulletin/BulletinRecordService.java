package com.czb.hn.service.bulletin;

import com.czb.hn.dto.bulletin.BulletinGenerationRecordDto;
import com.czb.hn.dto.bulletin.BulletinPushRecordDto;
import com.czb.hn.dto.bulletin.ReceiveInfoBulletinPrepareDto;
import com.czb.hn.dto.common.PageResult;
import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 简报记录服务接口
 * 负责简报生成记录和推送记录的管理
 */
public interface BulletinRecordService {


    /**
     * 根据生成记录ID获取推送记录（分页）
     *
     * @param generationId 生成记录ID
     * @param page         页码（从0开始）
     * @param size         每页大小
     * @return 推送记录分页结果
     */
    PageResult<BulletinPushRecordDto> getPushRecordsByGenerationId(
            Long generationId,
            int page,
            int size);

    ReceiveInfoBulletinPrepareDto getReceiveInfoBulletinPrepareDto(Long generationId);

    /**
     * 手动推送简报
     *
     * @param generationId 生成记录ID
     * @return 推送记录ID列表
     */
    List<Long> manualPushBulletin(Long generationId, ReceiveInfoBulletinPrepareDto receiveInfoBulletinPrepareDto);

    /**
     * 获取简报内容（PDF二进制数据）
     *
     * @param generationId 生成记录ID
     * @return 简报内容
     */
    byte[] getBulletinContent(Long generationId);

    /**
     * 获取简报文件预签名URL
     *
     * @param generationId  生成记录ID
     * @param expirySeconds URL有效期（秒）
     * @return 预签名URL
     */
    String getBulletinFileUrl(Long generationId, int expirySeconds);

    /**
     * 根据时间范围和简报类型查询生成记录（分页）
     * 两个条件均为可选，如果均未指定则返回最近30天的所有记录
     *
     * @param startTime    开始时间（可选）
     * @param endTime      结束时间（可选）
     * @param bulletinType 简报类型（可选）
     * @param page         页码（从0开始）
     * @param size         每页大小
     * @return 简报生成记录分页结果
     */
    PageResult<BulletinGenerationRecordDto> searchGenerationRecords(
            LocalDateTime startTime,
            LocalDateTime endTime,
            String bulletinType,
            int page,
            int size);

    /**
     * 根据方案ID、时间范围和简报类型查询生成记录（分页）
     * 时间范围和简报类型均为可选，如果未指定则返回最近30天的所有记录
     *
     * @param planId       方案ID
     * @param startTime    开始时间（可选）
     * @param endTime      结束时间（可选）
     * @param bulletinType 简报类型（可选）
     * @param page         页码（从0开始）
     * @param size         每页大小
     * @return 简报生成记录分页结果
     */
    PageResult<BulletinGenerationRecordDto> searchGenerationRecordsByPlanId(
            Long planId,
            LocalDateTime startTime,
            LocalDateTime endTime,
            String bulletinType,
            int page,
            int size);

    /**
     * 保存简报内容到MinIO
     *
     * @param generationId 生成记录ID
     * @param content      简报内容
     * @param filename     文件名
     */
    void saveBulletinContent(BulletinGenerationRecordEntity generationRecord, byte[] content, String filename);

    /**
     * 获取简报文件信息
     *
     * @param generationId 生成记录ID
     * @return 文件信息
     */
    FileInfo getBulletinFileInfo(Long generationId);

    /**
     * 简报文件信息
     */
    record FileInfo(String fileId, String fileName, Long fileSize) {}

    /**
     * 删除简报生成记录（逻辑删除）
     * 
     * @param id 生成记录ID
     */
    void deleteGenerationRecord(Long id);

    /**
     * 批量删除简报生成记录（逻辑删除）
     * 
     * @param ids 生成记录ID集合
     */
    void deleteGenerationRecords(List<Long> ids);

    /**
     * 批量下载简报PDF，返回压缩包字节流
     * 
     * @param ids 生成记录ID集合
     * @return 压缩包字节流
     */
    byte[] batchDownloadBulletins(List<Long> ids);



}