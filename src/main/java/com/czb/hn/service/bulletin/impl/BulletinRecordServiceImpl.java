package com.czb.hn.service.bulletin.impl;

import com.czb.hn.dto.bulletin.BulletinGenerationRecordDto;
import com.czb.hn.dto.bulletin.BulletinParams;
import com.czb.hn.dto.bulletin.BulletinPushRecordDto;
import com.czb.hn.dto.bulletin.ReceiveInfoBulletinPrepareDto;
import com.czb.hn.dto.common.PageResult;
import com.czb.hn.dto.file.FileStorageDto;
import com.czb.hn.dto.recipients.ReceiveInfoBulletinDto;
import com.czb.hn.dto.recipients.RecipientsBulletinDto;
import com.czb.hn.enums.PushMethod;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushSystemType;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;
import com.czb.hn.jpa.securadar.entity.BulletinPushRecordEntity;
import com.czb.hn.jpa.securadar.entity.JobEntity;
import com.czb.hn.jpa.securadar.repository.BulletinGenerationRecordRepository;
import com.czb.hn.jpa.securadar.repository.BulletinPushRecordRepository;
import com.czb.hn.jpa.securadar.repository.JobRepository;
import com.czb.hn.service.bulletin.BulletinRecordService;
import com.czb.hn.service.business.RecipientService;
import com.czb.hn.service.file.FileStorageService;
import com.czb.hn.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.czb.hn.service.bulletin.BulletinEmailService;

/**
 * 简报记录服务实现类
 */
@Service
public class BulletinRecordServiceImpl implements BulletinRecordService {

    private static final Logger log = LoggerFactory.getLogger(BulletinRecordServiceImpl.class);

    private final BulletinGenerationRecordRepository generationRecordRepository;

    private final BulletinPushRecordRepository pushRecordRepository;

    private final JobRepository jobRepository;

    private final FileStorageService fileStorageService;
    private final RecipientService recipientService;
    private final BulletinEmailService bulletinEmailService;

    public BulletinRecordServiceImpl(BulletinGenerationRecordRepository generationRecordRepository,
            BulletinPushRecordRepository pushRecordRepository, JobRepository jobRepository,
            FileStorageService fileStorageService, RecipientService recipientService,
            BulletinEmailService bulletinEmailService) {
        this.generationRecordRepository = generationRecordRepository;
        this.pushRecordRepository = pushRecordRepository;
        this.jobRepository = jobRepository;
        this.fileStorageService = fileStorageService;
        this.recipientService = recipientService;
        this.bulletinEmailService = bulletinEmailService;
    }

    @Override
    public PageResult<BulletinPushRecordDto> getPushRecordsByGenerationId(
            Long generationId,
            int page,
            int size) {
        Assert.notNull(generationId, "生成记录ID不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "pushTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinPushRecordEntity> entityPage = pushRecordRepository.findByGenerationId(generationId, pageRequest);

        // 将Entity转换为DTO
        List<BulletinPushRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToPushDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public ReceiveInfoBulletinPrepareDto getReceiveInfoBulletinPrepareDto(Long generationId) {
        BulletinGenerationRecordEntity generationRecord = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        ReceiveInfoBulletinDto receiveInfoBulletin = recipientService.getReceiveInfoBulletin(
                generationRecord.getPlanId(), PushSystemType.fromString(generationRecord.getBulletinType()),
                PushType.EMAIL);
        ReceiveInfoBulletinDto receiveInfoBulletinSms = recipientService.getReceiveInfoBulletin(
                generationRecord.getPlanId(), PushSystemType.fromString(generationRecord.getBulletinType()),
                PushType.SMS);
        return new ReceiveInfoBulletinPrepareDto(receiveInfoBulletin, receiveInfoBulletinSms);
    }

    @Override
    @Transactional
    public List<Long> manualPushBulletin(Long generationId,
            ReceiveInfoBulletinPrepareDto receiveInfoBulletinPrepareDto) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        // 调用DAO层获取Entity
        BulletinGenerationRecordEntity generationRecord = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        // 检查是否有内容可推送
        if (generationRecord.getFileObjectName() == null) {
            throw new IllegalArgumentException("生成记录没有内容可推送");
        }

        // 根据generationId查询关联任务信息，获取接收人列表
        ReceiveInfoBulletinDto emailReceivers = receiveInfoBulletinPrepareDto.emailReceiveInfoBulletinDto();
        ReceiveInfoBulletinDto smsReceivers = receiveInfoBulletinPrepareDto.smsReceiveInfoBulletinDto();

        if ((emailReceivers == null || emailReceivers.enable() == null || !emailReceivers.enable()  
                || emailReceivers.recipients() == null || emailReceivers.recipients().isEmpty()) 
            && (smsReceivers == null || smsReceivers.enable() == null || !smsReceivers.enable() 
                || smsReceivers.recipients() == null || smsReceivers.recipients().isEmpty())) {
            throw new IllegalArgumentException("必须至少配置一种接收方式（邮件或短信）");
        }

        List<Long> pushRecordIds = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 发送邮件
        if (emailReceivers.enable() != null && emailReceivers.enable()) {
            for (RecipientsBulletinDto email : emailReceivers.recipients()) {
                if (email.enable() == null || !email.enable()) {
                    continue;
                }
                // 创建Entity
                BulletinPushRecordEntity pushRecord = createPushRecordEntity(
                        generationId,
                        email.address(),
                        PushType.EMAIL.name(),
                        PushMethod.MANUAL.name(),
                        now);

                // 调用DAO层保存Entity
                BulletinPushRecordEntity savedRecord = pushRecordRepository.save(pushRecord);
                pushRecordIds.add(savedRecord.getId());

                // 使用新的邮件服务发送邮件
                try {
                    boolean success = bulletinEmailService.sendBulletinEmail(generationId, email.address());
                    
                    // 更新推送状态
                    updatePushRecordStatus(savedRecord, success ? PushStatus.SUCCESS : PushStatus.FAILURE);
                } catch (Exception e) {
                    log.error("发送邮件失败: {}", email.address(), e);
                    
                    // 更新推送状态为失败
                    updatePushRecordStatus(savedRecord, PushStatus.FAILURE);
                }
            }
        }

        // 发送短信（保留原有逻辑）
        if (smsReceivers.enable() != null && smsReceivers.enable()) {
            for (RecipientsBulletinDto sms : smsReceivers.recipients()) {
                if (sms.enable() == null || !sms.enable()) {
                    continue;
                }
                // 创建Entity
                BulletinPushRecordEntity pushRecord = createPushRecordEntity(
                        generationId,
                        sms.address(),
                        PushType.SMS.name(),
                        PushMethod.MANUAL.name(),
                        now);

                // 调用DAO层保存Entity
                BulletinPushRecordEntity savedRecord = pushRecordRepository.save(pushRecord);
                pushRecordIds.add(savedRecord.getId());

                // 实际发送短信的逻辑
                try {
                    // TODO: 调用短信服务发送短信
                    // String smsContent = generationRecord.getBulletinTitle() + " 已生成，请登录系统查看详情。";
                    // smsService.sendSms(phone, smsContent);

                    // 更新推送状态为成功
                    updatePushRecordStatus(savedRecord, PushStatus.SUCCESS);
                } catch (Exception e) {
                    log.error("发送短信失败: {}", sms.address(), e);

                    // 更新推送状态为失败
                    updatePushRecordStatus(savedRecord, PushStatus.FAILURE);
                }
            }
        }

        return pushRecordIds;
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> searchGenerationRecords(
            LocalDateTime startTime,
            LocalDateTime endTime,
            String bulletinType,
            int page,
            int size) {
        // 参数校验
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 如果未指定时间范围，默认查询最近30天的记录
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(30);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage = generationRecordRepository
                .findByGenerationTimeBetweenAndBulletinType(
                        startTime,
                        endTime,
                        (bulletinType != null && !bulletinType.isBlank()) ? bulletinType : null,
                        pageRequest);

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> searchGenerationRecordsByPlanId(
            Long planId,
            LocalDateTime startTime,
            LocalDateTime endTime,
            String bulletinType,
            int page,
            int size) {
        // 参数校验
        Assert.notNull(planId, "方案ID不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 如果未指定时间范围，默认查询最近30天的记录
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(30);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage;
        if (bulletinType != null && !bulletinType.isBlank()) {
            entityPage = generationRecordRepository.findByPlanIdAndGenerationTimeBetweenAndBulletinType(
                    planId, startTime, endTime, bulletinType, pageRequest);
        } else {
            entityPage = generationRecordRepository.findByPlanIdAndGenerationTimeBetween(
                    planId, startTime, endTime, pageRequest);
        }

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public byte[] getBulletinContent(Long generationId) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        // 从数据库获取生成记录
        BulletinGenerationRecordEntity record = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        if (record.getFileObjectName() == null) {
            throw new IllegalStateException("简报文件不存在");
        }

        // 通过文件ID获取文件内容
        return fileStorageService.getFileContent(record.getFileObjectName());
    }

    @Override
    public String getBulletinFileUrl(Long generationId, int expirySeconds) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        // 从数据库获取文件对象名
        BulletinGenerationRecordEntity record = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        if (record.getFileObjectName() == null) {
            throw new IllegalStateException("简报文件不存在");
        }

        // 生成预签名URL
        return "";
    }

    @Override
    @Transactional
    public void saveBulletinContent(BulletinGenerationRecordEntity record, byte[] content, String filename) {
        Assert.notNull(record, "生成记录不能为空");
        Assert.notNull(content, "简报内容不能为空");
        Assert.hasText(filename, "文件名不能为空");

        // 上传文件并获取文件ID
        String fileId = fileStorageService.storeFile(
                content,
                filename,
                "application/pdf",
                "bulletin",
                record.getId(),
                "content");

        // 更新生成记录
        record.setFileObjectName(fileId); // 存储文件ID而非对象名
        record.setFileSize((long) content.length);
        record.setStatus("SUCCESS");
        record.setUpdatedAt(LocalDateTime.now());
        generationRecordRepository.save(record);

        log.info("简报内容保存成功，生成记录ID: {}, 文件ID: {}", record.getId(), fileId);
    }

    @Override
    public FileInfo getBulletinFileInfo(Long generationId) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        // 从数据库获取生成记录
        BulletinGenerationRecordEntity record = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        if (record.getFileObjectName() == null) {
            throw new IllegalStateException("简报文件不存在");
        }

        // 获取文件信息
        FileStorageDto fileInfo = fileStorageService.getFileByBusiness("bulletin", generationId, "content");
        if (fileInfo == null) {
            throw new IllegalStateException("简报文件信息不存在");
        }

        return new FileInfo(
                fileInfo.fileId(),
                fileInfo.originalName(),
                fileInfo.fileSize());
    }

    @Override
    @Transactional
    public void deleteGenerationRecord(Long id) {
        Assert.notNull(id, "生成记录ID不能为空");
        generationRecordRepository.logicDeleteById(id);
    }

    @Override
    @Transactional
    public void deleteGenerationRecords(List<Long> ids) {
        Assert.notEmpty(ids, "ID集合不能为空");
        generationRecordRepository.logicDeleteByIdIn(ids);
    }

    @Override
    public byte[] batchDownloadBulletins(List<Long> ids) {
        Assert.notEmpty(ids, "ID集合不能为空");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            for (Long id : ids) {
                BulletinGenerationRecordEntity record = generationRecordRepository.findById(id)
                        .orElse(null);
                if (record == null || Boolean.TRUE.equals(record.getIsDeleted())) {
                    log.warn("跳过不存在或已删除的记录: {}", id);
                    continue;
                }

                String fileId = record.getFileObjectName();
                if (fileId == null) {
                    log.warn("记录 {} 没有关联文件", id);
                    continue;
                }

                // 获取文件信息
                FileStorageDto fileInfo = fileStorageService.getFileByBusiness("bulletin", id, "content");
                if (fileInfo == null) {
                    log.warn("未找到记录 {} 的文件信息", id);
                    continue;
                }

                // 获取文件内容
                byte[] fileBytes;
                try {
                    fileBytes = fileStorageService.getFileContent(fileId);
                } catch (Exception e) {
                    log.error("获取文件内容失败, 记录ID: {}, 文件ID: {}", id, fileId, e);
                    continue;
                }

                // 使用原始文件名或生成文件名
                String fileName = fileInfo.originalName();
                if (fileName == null || fileName.isEmpty()) {
                    fileName = (record.getBulletinTitle() != null ? record.getBulletinTitle() : ("bulletin_" + id))
                        + ".pdf";
                }

                // 添加到ZIP
                ZipEntry entry = new ZipEntry(fileName);
                zos.putNextEntry(entry);
                zos.write(fileBytes);
                zos.closeEntry();
                log.debug("已添加文件到ZIP: {}", fileName);
            }
            zos.finish();
        } catch (IOException e) {
            log.error("压缩文件生成失败", e);
            throw new RuntimeException("压缩文件生成失败: " + e.getMessage(), e);
        }
        return baos.toByteArray();
    }

    /**
     * 将生成记录实体转换为DTO
     */
    private BulletinGenerationRecordDto convertToGenerationDto(BulletinGenerationRecordEntity entity) {
        // 检查是否可以推送（有文件且状态正常）
        boolean canPush = entity.getFileObjectName() != null && "SUCCESS".equals(entity.getStatus());

        return new BulletinGenerationRecordDto(
                entity.getId(),
                entity.getBulletinTitle(),
                entity.getBulletinType(),
                entity.getStartTime(),
                entity.getEndTime(),
                entity.getGenerationTime(),
                entity.getJobId(),
                entity.getPlanId(),
                canPush,
                entity.getStatus());
    }

    /**
     * 创建推送记录实体
     */
    private BulletinPushRecordEntity createPushRecordEntity(Long generationId, String account, String pushType,
            String pushMethod, LocalDateTime now) {
        BulletinPushRecordEntity pushRecord = new BulletinPushRecordEntity();
        pushRecord.setGenerationId(generationId);
        pushRecord.setAccount(account);
        pushRecord.setPushType(pushType);
        pushRecord.setPushMethod(pushMethod);
        pushRecord.setPushTime(now);
        pushRecord.setStatus(PushStatus.PENDING.name());
        pushRecord.setCreatedAt(now);
        pushRecord.setUpdatedAt(now);
        return pushRecord;
    }

    /**
     * 更新推送记录状态
     */
    private void updatePushRecordStatus(BulletinPushRecordEntity pushRecord, PushStatus status) {
        pushRecord.setStatus(status.name());
        pushRecord.setUpdatedAt(LocalDateTime.now());
        pushRecordRepository.save(pushRecord);
    }

    /**
     * 将推送记录实体转换为DTO
     */
    private BulletinPushRecordDto convertToPushDto(BulletinPushRecordEntity entity) {
        return new BulletinPushRecordDto(
                entity.getId(),
                entity.getGenerationId(),
                entity.getAccount(),
                entity.getPushType(),
                entity.getPushMethod(),
                entity.getPushTime(),
                entity.getStatus());
    }
}