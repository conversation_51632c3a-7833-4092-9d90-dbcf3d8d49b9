package com.czb.hn.service.collector.impl;

import com.czb.hn.config.ScheduleConfig;
import com.czb.hn.dto.sina.data.SinaDataRequestDto;
import com.czb.hn.dto.sina.data.SinaDataResponseDto;
import com.czb.hn.dto.sina.data.SinaNewsContentDto;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.DetailedSourceType;
import com.czb.hn.enums.EmotionType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MatchType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.ResultViewType;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.ThirdLevelSourceType;
import com.czb.hn.enums.UserVerificationType;
import com.czb.hn.jpa.securadar.entity.*;
import com.czb.hn.jpa.securadar.repository.SinaNewsOdsRepository;
import com.czb.hn.service.collector.SinaNewsCollectorService;
import com.czb.hn.service.sina.SinaAuthService;
import com.czb.hn.util.DateTimeUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.StringReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 新浪舆情数据收集器服务实现
 */
@Service
public class SinaNewsCollectorServiceImpl implements SinaNewsCollectorService {

    private static final Logger logger = LoggerFactory.getLogger(SinaNewsCollectorServiceImpl.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SinaAuthService sinaAuthService;

    @Autowired
    private SinaNewsOdsRepository sinaNewsOdsRepository;

    @Autowired
    private ScheduleConfig scheduleConfig;

    @Value("${sina.api.data.url:https://api-open-wx-www.yqt365.com/dataapp/api/data/kafka/scribe/poll}")
    private String dataApiUrl;

    @Value("${sina.api.default.ticket}")
    private String defaultTicket;

    @Value("${sina.api.default.fetch.limit:500}")
    private Integer defaultFetchLimit;

    @Value("${sina.api.use.local.file:false}")
    private Boolean useLocalFile;

    @Value("${sina.api.local.file.path:docs/yuqing.txt}")
    private String localFilePath;

    @Override
    public List<SinaDataResponseDto> fetchData(SinaDataRequestDto requestDto) {
        try {
            String accessToken = sinaAuthService.getCurrentAccessToken();

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("accessToken", accessToken);
            params.add("ticket", requestDto.ticket());
            params.add("offset", requestDto.offset().toString());

            if (requestDto.pollType() != null) {
                params.add("pollType", requestDto.pollType().toString());
            }

            if (requestDto.num() != null) {
                params.add("num", requestDto.num().toString());
            }

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/x-www-form-urlencoded");

            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    dataApiUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class);

            if (response.getBody() != null) {
                return parseResponseBody(response.getBody());
            } else {
                logger.error("Failed to fetch data from Sina API. Response body is null.");
                return new ArrayList<>();
            }
        } catch (Exception e) {
            logger.error("Error fetching data from Sina API: {}", e.getMessage(), e);
            throw new RuntimeException("Error fetching data from Sina API", e);
        }
    }

    @Override
    @Scheduled(cron = "#{@scheduleConfig.collector.enabled ? @scheduleConfig.collector.cron : '-'}")
    public void scheduledFetch() {
        // 检查是否启用定时任务
        if (!scheduleConfig.getCollector().isEnabled()) {
            logger.debug("Data collector scheduled task is disabled, skipping execution");
            return;
        }

        try {
            logger.info("Starting scheduled data fetch from Sina API (Environment: {})",
                    scheduleConfig.getCollector().getEnvironment());

            Long latestOffset = getLatestOffset();
            Integer batchSize = scheduleConfig.getCollector().getBatchSize() > 0
                    ? scheduleConfig.getCollector().getBatchSize()
                    : defaultFetchLimit;

            List<SinaDataResponseDto> dataList = fetchDataByTicket(defaultTicket, latestOffset, batchSize);

            if (dataList.isEmpty()) {
                logger.info("No new data available for fetching");
                return;
            }

            int savedCount = saveToOds(dataList);
            logger.info("Scheduled fetch completed. Environment: {}, Fetched: {}, Saved: {}",
                    scheduleConfig.getCollector().getEnvironment(), dataList.size(), savedCount);

        } catch (Exception e) {
            logger.error("Error in scheduled fetch (Environment: {}): {}",
                    scheduleConfig.getCollector().getEnvironment(), e.getMessage(), e);

            // 可以根据环境配置决定是否重试
            if (shouldRetryOnError()) {
                scheduleRetryFetch();
            }
        }
    }

    @Override
    public List<SinaDataResponseDto> fetchDataByTimeRange(LocalDateTime startTime, LocalDateTime endTime,
            Integer pageSize) {
        // 新浪舆情通API不支持按时间范围查询，可以实现从ODS层查询
        // 这里只是为了实现接口需要，实际不会被调用
        throw new UnsupportedOperationException("Sina API does not support query by time range directly");
    }

    @Override
    public List<SinaDataResponseDto> fetchDataByKeyword(String keyword, Integer pageSize) {
        // 新浪舆情通API不支持按关键词查询，因为关键词是在票据配置中设置的
        // 这里只是为了实现接口需要，实际不会被调用
        throw new UnsupportedOperationException("Sina API does not support query by keyword directly");
    }

    @Override
    public List<SinaDataResponseDto> fetchDataByTicket(String ticket, Long offset, Integer limit) {
        // 如果配置为使用本地文件，则从本地文件读取数据
        if (useLocalFile) {
            return fetchDataFromLocalFile(offset, limit);
        }

        SinaDataRequestDto requestDto = new SinaDataRequestDto(
                sinaAuthService.getCurrentAccessToken(),
                ticket,
                offset.intValue(),
                1, // 指定返回数据量
                limit);

        return fetchData(requestDto);
    }

    @Override
    public Long getLatestOffset() {
        Long offset = sinaNewsOdsRepository.findMaxDataOffset();
        return offset != null ? offset + 1 : 1L;
    }

    @Override
    public int saveToOds(List<SinaDataResponseDto> responseDataList) {
        if (responseDataList == null || responseDataList.isEmpty()) {
            logger.info("No data to save to ODS");
            return 0;
        }

        int savedCount = 0;
        int skipCount = 0;
        int errorCount = 0;

        for (SinaDataResponseDto data : responseDataList) {
            try {
                if (data.data() == null || data.offset() == null) {
                    logger.warn("Invalid data structure, missing data or offset");
                    continue;
                }

                SinaNewsContentDto content = data.data();

                // 检查内容ID是否已存在
                String contentId = content.contentId();
                if (contentId == null || contentId.isBlank()) {
                    logger.warn("Content ID is null or empty, skipping record with offset: {}", data.offset());
                    skipCount++;
                    continue;
                }

                Optional<SinaNewsOdsEntity> existingEntity = sinaNewsOdsRepository.findByContentId(contentId);
                if (existingEntity.isPresent()) {
                    logger.debug("Content ID already exists in ODS: {}", contentId);
                    skipCount++;
                    continue;
                }

                // 创建ODS实体
                SinaNewsOdsEntity odsEntity = mapToOdsEntity(content, data.offset());

                // 保存实体
                sinaNewsOdsRepository.save(odsEntity);
                savedCount++;

                if (savedCount % 100 == 0) {
                    logger.info("Saved {} records to ODS", savedCount);
                }

            } catch (Exception e) {
                logger.error("Error saving data to ODS: {}", e.getMessage(), e);
                errorCount++;
            }
        }

        logger.info("Completed saving to ODS. Total: {}, Saved: {}, Skipped: {}, Errors: {}",
                responseDataList.size(), savedCount, skipCount, errorCount);

        return savedCount;
    }

    /**
     * 判断是否应该在错误时重试
     */
    private boolean shouldRetryOnError() {
        // 可以根据环境配置决定重试策略
        String environment = scheduleConfig.getCollector().getEnvironment();
        return !"test".equalsIgnoreCase(environment) &&
                !"local".equalsIgnoreCase(environment);
    }

    /**
     * 安排重试任务
     */
    private void scheduleRetryFetch() {
        // 这里可以实现重试逻辑，比如延迟重试
        logger.info("Retry mechanism not implemented yet for environment: {}",
                scheduleConfig.getCollector().getEnvironment());
    }

    /**
     * 解析响应体，将换行符分隔的JSON字符串解析为响应对象列表
     */
    private List<SinaDataResponseDto> parseResponseBody(String responseBody) throws Exception {
        if (responseBody == null || responseBody.isBlank()) {
            logger.warn("Response body is null or empty");
            return List.of();
        }

        List<SinaDataResponseDto> results = new ArrayList<>();
        int lineCount = 0;
        int parsedCount = 0;
        int errorCount = 0;

        try (BufferedReader reader = new BufferedReader(new StringReader(responseBody))) {
            String line;
            while ((line = reader.readLine()) != null) {
                lineCount++;
                if (!line.isBlank()) {
                    try {
                        SinaDataResponseDto responseDto = objectMapper.readValue(line, SinaDataResponseDto.class);
                        if (responseDto != null && responseDto.data() != null) {
                            results.add(responseDto);
                            parsedCount++;
                        }
                    } catch (Exception e) {
                        logger.error("Error parsing response line {}: {}", lineCount, e.getMessage());
                        errorCount++;
                    }
                }
            }
        }

        logger.info("Parsed response body. Total lines: {}, Parsed: {}, Errors: {}",
                lineCount, parsedCount, errorCount);

        return results;
    }

    /**
     * 将内容DTO映射到ODS实体
     */
    private SinaNewsOdsEntity mapToOdsEntity(SinaNewsContentDto content, Long offset) throws Exception {
        SinaNewsOdsEntity entity = new SinaNewsOdsEntity();

        // 设置基本字段
        entity.setContentId(content.contentId());
        entity.setTextId(content.textId());
        entity.setAuthor(content.author());
        entity.setTitle(content.title());
        entity.setText(content.text());
        entity.setSummary(content.summary());
        entity.setUrl(content.url());
        entity.setSource(content.source());
        entity.setSourceWebsite(content.sourceWebsite());
        entity.setCaptureWebsite(content.captureWebsite());

        // 设置短链接
        if (content.shortUrl() != null && !content.shortUrl().isEmpty()) {
            entity.setShortUrl(objectMapper.writeValueAsString(content.shortUrl()));
        }

        // 设置时间字段
        if (content.publishTime() != null && !content.publishTime().isBlank()) {
            entity.setPublishTime(DateTimeUtil.parseDateTime(content.publishTime(), content.contentId()));
        }

        if (content.captureTime() != null && !content.captureTime().isBlank()) {
            entity.setCaptureTime(DateTimeUtil.parseDateTime(content.captureTime(), content.contentId()));
        }

        entity.setOriginType(SourceType.fromString(content.originType()));
        entity.setOriginTypeSecond(DetailedSourceType.fromString(content.originTypeSecond()));
        entity.setOriginTypeThird(ThirdLevelSourceType.fromString(content.originTypeThird()));


        // 设置匹配信息
        if (content.matchInfo() != null) {
            entity.setMatchType(MatchType.fromString(content.matchInfo().type()));
            entity.setMatchInfo(content.matchInfo().info());
            entity.setMatchTicket(content.matchInfo().ticket());
            entity.setMatchName(content.matchInfo().name());
        }

        // 设置用户信息
        if (content.userExt() != null) {
            entity.setUserAccount(content.userExt().accountNum());
            entity.setUserNickname(content.userExt().nickname());
            entity.setUserGender(content.userExt().gender());
            entity.setUserProfileImage(content.userExt().profileImageUrl());
            entity.setHomePage(content.userExt().homePage());
            entity.setUserProvince(content.userExt().province());
            entity.setUserCity(content.userExt().city());
            entity.setUserVerified(content.userExt().verified());
            entity.setUserVerifiedType(UserVerificationType.fromString(content.userExt().verifiedType()));
            entity.setTags(content.userExt().tags());
            entity.setContentsCount(content.userExt().contentsCount());
            entity.setFriendsCount(content.userExt().friendsCount());
            entity.setFollowersCount(content.userExt().followersCount());
            entity.setCreateTime(content.userExt().createTime());
        }

        // 设置内容扩展信息
        if (content.contentExt() != null) {
            entity.setSensitivityType(InformationSensitivityType.fromInteger(content.contentExt().sensitivityType()));
            entity.setSensitivityScore(content.contentExt().sensitivityScore());
            entity.setEmotion(EmotionType.fromChineseValue(content.contentExt().emotion()));
            entity.setIsOriginal(ContentCategory.fromString(content.contentExt().isOriginal()));
            entity.setResultView(ResultViewType.fromString(content.contentExt().isNormarlData()));
            entity.setSimilarityNum(content.contentExt().similarityNum());
            entity.setSimilarityTag(content.contentExt().similarityTag());
            entity.setCommentNum(content.contentExt().commentNum());
            entity.setForwardNum(content.contentExt().forwardNum());
            entity.setPraiseNum(content.contentExt().praiseNum());
            entity.setShareNum(content.contentExt().shareNum());
            entity.setCollectionNum(content.contentExt().collectionNum());
            entity.setAnswerNum(content.contentExt().answerNum());
            entity.setLookingNum(content.contentExt().lookingNum());
            entity.setInfoCategory(content.contentExt().newOriginType());
            entity.setInfoLevel(MediaLevel.fromChineseValue(content.contentExt().originLevel()));
            entity.setAreaInvolved(content.contentExt().contentProvince());

            // 处理行业标签
            try {
                if (content.contentExt().secondTrades() != null && !content.contentExt().secondTrades().isEmpty()) {
                    entity.setSecondTrades(objectMapper.writeValueAsString(content.contentExt().secondTrades()));
                }
            } catch (Exception e) {
                logger.warn("Error serializing secondTrades for content {}: {}", content.contentId(), e.getMessage());
            }

            entity.setContentTypes(ContentType.fromString(content.contentExt().contentTypes()));

            // 处理图片信息
            try {
                if (content.contentExt().images() != null && !content.contentExt().images().isEmpty()) {
                    entity.setImageUrls(objectMapper.writeValueAsString(content.contentExt().images()));
                }
            } catch (Exception e) {
                logger.warn("Error serializing images for content {}: {}", content.contentId(), e.getMessage());
            }

            // 地理位置信息
            entity.setPublishProvince(content.contentExt().publishProvince());
            entity.setPublishCity(content.contentExt().publishCity());
            entity.setContentProvince(content.contentExt().contentProvince());
            entity.setContentCity(content.contentExt().contentCity());
            entity.setLocation(content.contentExt().location());

            // 评论文本
            entity.setCommentText(content.contentExt().commentText());
            entity.setCommentForwardText(content.contentExt().commentForwardText());

            // OCR内容
            entity.setOcrContents(content.contentExt().ocrContents());

            // 注解信息
            entity.setAnnotations(content.contentExt().annotations());

            // 微博转发类型 - 注意可能是Integer类型，需要转换为String
            if (content.contentExt().wbForwardType() != null) {
                entity.setWbForwardType(String.valueOf(content.contentExt().wbForwardType()));
            }
        }

        // 设置视频信息
        if (content.videoExt() != null) {
            entity.setVideoUrl(content.videoExt().videoOriginUrl());
            entity.setVideoCoverUrl(content.videoExt().subjectAnalysis());
        }

        // 设置转发内容ID
        if (content.rootContent() != null) {
            entity.setRootContentId(content.rootContent().contentId());
        }

        // 设置原始JSON
        try {
            entity.setOriginalJson(objectMapper.writeValueAsString(content));
        } catch (Exception e) {
            logger.warn("Error serializing original JSON for content {}: {}", content.contentId(), e.getMessage());
        }

        // 设置偏移量和处理标记
        entity.setDataOffset(offset);
        entity.setProcessed(false);

        return entity;
    }

    /**
     * 从本地文件读取测试数据
     * 用于本地开发和测试，避免频繁调用远程API
     */
    private List<SinaDataResponseDto> fetchDataFromLocalFile(Long offset, Integer limit) {
        try {
            logger.info("Reading test data from local file: {}, offset: {}, limit: {}",
                    localFilePath, offset, limit);

            Path filePath = Paths.get(localFilePath);
            if (!Files.exists(filePath)) {
                logger.warn("Local test file does not exist: {}", localFilePath);
                return new ArrayList<>();
            }

            String content = Files.readString(filePath);
            List<SinaDataResponseDto> allData = parseResponseBody(content);

            // 模拟分页逻辑：根据offset和limit返回相应的数据
            int startIndex = Math.max(0, offset.intValue() - 1);
            int endIndex = Math.min(allData.size(), startIndex + limit);

            if (startIndex >= allData.size()) {
                logger.info("Offset {} exceeds available data size {}", offset, allData.size());
                return new ArrayList<>();
            }

            List<SinaDataResponseDto> result = allData.subList(startIndex, endIndex);
            logger.info("Loaded {} records from local file (requested offset: {}, limit: {})",
                    result.size(), offset, limit);

            return result;

        } catch (Exception e) {
            logger.error("Error reading data from local file {}: {}", localFilePath, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

}