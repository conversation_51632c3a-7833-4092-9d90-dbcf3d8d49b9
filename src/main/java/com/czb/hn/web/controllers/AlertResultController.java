package com.czb.hn.web.controllers;

import com.czb.hn.constant.CommonConstants;
import com.czb.hn.dto.alert.AlertSearchCriteriaDto;
import com.czb.hn.dto.alert.AlertSearchResultDto;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.dto.export.AlertResultExportDto;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.AlertSearchService;
import com.czb.hn.util.EasyExcelExportUtil;
import com.czb.hn.util.FileNameUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.IntStream;

/**
 * Alert Result Controller
 * 预警结果管理控制器，提供预警结果的查询、搜索和统计API
 */
@RestController
@RequestMapping("/alert-results")
@Tag(name = "AlertResultController", description = "预警结果查询、搜索和统计API")
@Slf4j
public class AlertResultController {

        @Autowired
        private AlertSearchService alertSearchService;

        /**
         * 搜索预警结果
         * 支持时间范围、信息敏感性类型、预警级别、来源信息、内容类别和关键词搜索
         * 结果按预警时间降序排列（最新的在前）
         */
        @GetMapping("/search")
        @RolesAllowed(value = { CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN,
                        CommonConstants.ENTERPRISE_USER })
        @Operation(summary = "搜索预警结果", description = "支持多维度过滤和关键词搜索，结果按预警时间降序排列")
        public ResponseEntity<ApiResponse<AlertSearchResultDto>> searchAlerts(
                        @Parameter(description = "方案id") Long planId,
                        @Parameter(description = "信息属性") @RequestParam(required = false) Integer informationSensitivityType,
                        @Parameter(description = "预警级别 SEVERE MODERATE SEVERE") @RequestParam(required = false) String warningLevel,
                        @Parameter(description = "来源类型") @RequestParam(required = false) String sourceType,
                        @Parameter(description = "开始时间 yyyy-MM-dd HH:mm:ss") @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                        @Parameter(description = "结束时间 yyyy-MM-dd HH:mm:ss") @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
                        @Parameter(description = "搜索文本") @RequestParam(required = false) String searchText,
                        @Parameter(description = "页码") Integer page,
                        @Parameter(description = "页大小") Integer size) {

                AlertSearchCriteriaDto criteria = new AlertSearchCriteriaDto(planId, informationSensitivityType,
                                warningLevel, sourceType, startTime, endTime, searchText, page, size);
                try {
                        log.info("Searching alerts with criteria: planId={}, sensitivityTypes={}, so={}, warningLevel={}",
                                        criteria.planId(), criteria.informationSensitivityType(),
                                        criteria.sourceType(), criteria.warningLevel());

                        AlertSearchResultDto result = alertSearchService.searchAlerts(criteria);
                        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "搜索成功", result));

                } catch (IllegalArgumentException e) {
                        log.warn("Invalid search criteria: {}", e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                        .body(new ApiResponse<>("ERROR", "搜索条件无效: " + e.getMessage(), null));
                } catch (Exception e) {
                        log.error("Error searching alerts: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
                }
        }

        /**
         * 批量导出预警结果
         * 根据预警ID列表导出Excel文件
         */
        @PostMapping("/export")
        @RolesAllowed(value = { CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN,
                        CommonConstants.ENTERPRISE_USER })
        @Operation(summary = "批量导出预警结果", description = "根据预警ID列表导出Excel文件")
        public void exportAlerts(
                        @Parameter(description = "预警ID列表") @RequestBody List<Long> alertIds,
                        HttpServletResponse response) {
                try {
                        log.info("Exporting alerts with IDs: {}", alertIds);

                        if (alertIds == null || alertIds.isEmpty()) {
                                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                                response.getWriter().write("预警ID列表不能为空");
                                return;
                        }

                        // 查询预警数据
                        List<AlertResultResponseDto> alerts = alertSearchService.getAlertsByIds(alertIds);

                        if (alerts.isEmpty()) {
                                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                                response.getWriter().write("未找到指定的预警记录");
                                return;
                        }

                        // 转换为导出DTO，将枚举值转换为中文描述
                        List<AlertResultExportDto> exportData = IntStream.range(0, alerts.size())
                                .mapToObj(i -> AlertResultExportDto.fromResponseDto(alerts.get(i), i + 1))
                                .toList();


                        // 使用EasyExcel生成Excel文件
                        byte[] excelBytes = EasyExcelExportUtil.exportToExcel(exportData, AlertResultExportDto.class,
                                        "预警结果");

                        // 设置响应头
                        FileNameUtil.FileNamePair fileNames = FileNameUtil.generateAlertFileName();
                        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                        response.setHeader("Content-Disposition", fileNames.getContentDisposition());
                        response.setContentLength(excelBytes.length);

                        // 写入响应
                        response.getOutputStream().write(excelBytes);
                        response.getOutputStream().flush();

                        log.info("Successfully exported {} alerts to Excel", alerts.size());

                } catch (IllegalArgumentException e) {
                        log.warn("Invalid export request: {}", e.getMessage());
                        try {
                                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                                response.getWriter().write("请求参数无效: " + e.getMessage());
                        } catch (IOException ioException) {
                                log.error("Error writing error response", ioException);
                        }
                } catch (Exception e) {
                        log.error("Error exporting alerts: {}", e.getMessage(), e);
                        try {
                                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                                response.getWriter().write("导出失败: " + e.getMessage());
                        } catch (IOException ioException) {
                                log.error("Error writing error response", ioException);
                        }
                }
        }

}
