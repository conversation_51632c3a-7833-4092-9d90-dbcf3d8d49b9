package com.czb.hn.web.controllers;

import com.czb.hn.constant.CommonConstants;
import com.czb.hn.dto.export.AlertResultExportDto;
import com.czb.hn.dto.response.*;
import com.czb.hn.dto.response.search.SearchRequestDto;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;
import com.czb.hn.dto.response.search.SearchResult;
import com.czb.hn.dto.export.MonitorDetailExportDto;
import com.czb.hn.enums.SimilarityArticleMerger;
import com.czb.hn.service.business.ElasticsearchSearchService;
import com.czb.hn.util.EasyExcelExportUtil;
import com.czb.hn.util.FileNameUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

/**
 * 舆情监测控制器
 * 提供基于Elasticsearch的舆情数据搜索API
 */
@RestController
@RequestMapping("/monitor")
@Tag(name = "MonitorResultController", description = "提供舆情监测的各种搜索功能")
public class MonitorResultController {

        private static final Logger logger = LoggerFactory.getLogger(MonitorResultController.class);

        @Autowired
        private ElasticsearchSearchService elasticsearchService;

        @GetMapping("/search")
        @Operation(summary = "搜索", description = "组合多个条件搜索舆情数据")
        @RolesAllowed(value = { CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN,
                        CommonConstants.ENTERPRISE_USER })
        public ResponseEntity<ApiResponse<SearchResult>> monitorSearch(
                        @Parameter(description = "方案ID") @RequestParam() Long planId,
                        @Parameter(description = "开始时间") @RequestParam() @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
                        @Parameter(description = "结束时间") @RequestParam() @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime,
                        @Parameter(description = "排序规则") @RequestParam() Integer sortRule,
                        @Parameter(description = "信息属性") Integer sensitivityType,
                        @Parameter(description = "相似文章合并") @RequestParam() Integer similarityArticleMer,
                        @Parameter(description = "匹配方式") @RequestParam() Integer matchMethod,
                        @Parameter(description = "媒体类型") @RequestParam(required = false) List<String> mediaTypes,
                        @Parameter(description = "二级来源类型") @RequestParam(required = false) List<String> mediaTypeSeconds,
                        @Parameter(description = "三级来源类型") @RequestParam(required = false) List<String> mediaTypeThirds,
                        @Parameter(description = "内容包含") @RequestParam(required = false) List<Integer> contentTypes,
                        @Parameter(description = "内容类型") @RequestParam(required = false) List<Integer> isOriginals,
                        @Parameter(description = "图文识别") @RequestParam(required = false) Integer imageTextMode,
                        @Parameter(description = "行业信息") @RequestParam(required = false) List<String> secondTrades,
                        @Parameter(description = "粉丝数下限") @RequestParam(required = false) Long authorFollowersCountMin,
                        @Parameter(description = "粉丝数上限") @RequestParam(required = false) Long authorFollowersCountMax,
                        @Parameter(description = "信源级别") @RequestParam(required = false) List<String> mediaLevels,
                        @Parameter(description = "分页大小") @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                        @Parameter(description = "当前页码") @RequestParam(required = false, defaultValue = "1") Integer pageNum) {
                try {
                        logger.info("高级搜索: planId={}, startTime={}, endTime={}, sortRule={}, sensitivityTypes={}, similarityArticleMer={}, matchMethod={}, mediaTypes={}, mediaTypeSecond={}, contentType={}, isOriginals={}, imageTextMode={}, secondTrades={}, authorFollowersCountMin={}, authorFollowersCountMax={}, mediaLevels={}, pageSize={}, pageNum={}",
                                        planId, startTime, endTime, sortRule, sensitivityType, similarityArticleMer,
                                        matchMethod, mediaTypes,
                                        mediaTypeSeconds, contentTypes, isOriginals, imageTextMode, secondTrades,
                                        authorFollowersCountMin,
                                        authorFollowersCountMax, mediaLevels, pageSize, pageNum);

                        SearchRequestDto searchRequestDto = new SearchRequestDto();
                        searchRequestDto.setPlanId(planId);
                        searchRequestDto.setStartTime(startTime);
                        searchRequestDto.setEndTime(endTime);
                        searchRequestDto.setSortRule(sortRule);
                        searchRequestDto.setSensitivityType(List.of(sensitivityType));
                        searchRequestDto.setSimilarityDisplayRule(
                                        SimilarityArticleMerger.MERGER.getValue().equals(similarityArticleMer));
                        searchRequestDto.setMatchMethod(matchMethod);
                        searchRequestDto.setMediaTypes(mediaTypes);
                        searchRequestDto.setMediaTypeSecond(mediaTypeSeconds);
                        searchRequestDto.setMediaTypeThirds(mediaTypeThirds);
                        searchRequestDto.setContentType(contentTypes);
                        searchRequestDto.setIsOriginal(isOriginals);
                        searchRequestDto.setImageTextMode(imageTextMode);
                        searchRequestDto.setSecondTrades(secondTrades);
                        searchRequestDto.setAuthorFollowersCountMin(authorFollowersCountMin);
                        searchRequestDto.setAuthorFollowersCountMax(authorFollowersCountMax);
                        searchRequestDto.setMediaLevel(mediaLevels);

                        List<SinaNewsSearchResponseDto> responseDtos = elasticsearchService.SinaNewsMonitor(
                                        searchRequestDto, pageSize, pageNum);

                        Map<String, Long> responseMediaDistribution = elasticsearchService
                                        .advancedSearchMediaDistribution(searchRequestDto);

                        // Safely extract total count with null checking
                        Long totalCountLong = responseMediaDistribution.get("全部");
                        if (totalCountLong == null) {
                                totalCountLong = 0L;
                        }

                        // Validate pageSize to prevent division by zero
                        if (pageSize <= 0) {
                                throw new IllegalArgumentException("页数必须为正数");
                        }

                        // Safely convert Long to Integer with bounds checking
                        Integer totalCount;
                        if (totalCountLong > Integer.MAX_VALUE) {
                                totalCount = Integer.MAX_VALUE;
                        } else {
                                totalCount = totalCountLong.intValue();
                        }

                        // Calculate total pages using ceiling division
                        Integer totalPageNum = (totalCount + pageSize - 1) / pageSize;

                        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "搜索成功",
                                        new SearchResult(responseDtos, responseMediaDistribution,
                                                        totalPageNum)));
                } catch (IllegalArgumentException e) {
                        logger.warn("搜索参数错误: {}", e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                        .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
                } catch (Exception e) {
                        logger.error("搜索失败: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
                }
        }

        @GetMapping("/detail")
        @Operation(summary = "详情", description = "获取信息详情")
        @RolesAllowed(value = { CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN,
                        CommonConstants.ENTERPRISE_USER })
        public ResponseEntity<ApiResponse<SinaNewsDetailResponseDto>> getNewsDetail(
                        @Parameter(description = "内容id") @RequestParam String contentId,
                        @Parameter(description = "方案ID") @RequestParam Long planId) {
                try {
                        logger.info("获取信息详情: contentId={}, planId={}", contentId, planId);

                        SinaNewsDetailResponseDto responseDto = elasticsearchService.getNewsDetail(contentId, planId);

                        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", responseDto));
                } catch (IllegalArgumentException e) {
                        logger.warn("信息详情参数错误: {}", e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                        .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
                } catch (Exception e) {
                        logger.error("信息详情获取失败: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
                }
        }

        /**
         * 批量导出监控结果
         * 根据内容ID列表导出Excel文件
         */
        @PostMapping("/export")
        @Operation(summary = "批量导出监控结果", description = "根据内容ID列表导出Excel文件")
        @RolesAllowed(value = { CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN,
                        CommonConstants.ENTERPRISE_USER })
        public void exportMonitorResults(
                        @Parameter(description = "内容ID列表") @RequestBody List<String> contentIds,
                        @Parameter(description = "方案ID") @RequestParam Long planId,
                        HttpServletResponse response) {
                try {
                        logger.info("Exporting monitor results with contentIds: {}, planId: {}", contentIds, planId);

                        if (contentIds == null || contentIds.isEmpty()) {
                                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                                response.getWriter().write("内容ID列表不能为空");
                                return;
                        }

                        if (planId == null) {
                                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                                response.getWriter().write("方案ID不能为空");
                                return;
                        }

                        // 批量查询监控数据
                        List<SinaNewsDetailResponseDto> monitorResults = new ArrayList<>();
                        for (String contentId : contentIds) {
                                try {
                                        SinaNewsDetailResponseDto detail = elasticsearchService.getNewsDetail(contentId,
                                                        planId);
                                        if (detail != null && detail.getContentId() != null) {
                                                monitorResults.add(detail);
                                        } else {
                                                logger.warn("No data found for contentId: {}", contentId);
                                        }
                                } catch (Exception e) {
                                        logger.warn("Error getting detail for contentId {}: {}", contentId,
                                                        e.getMessage());
                                }
                        }

                        if (monitorResults.isEmpty()) {
                                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                                response.getWriter().write("未找到指定的监控记录");
                                return;
                        }

                        // 转换为导出DTO，将枚举值转换为中文描述
                        List<MonitorDetailExportDto> exportData = IntStream.range(0, monitorResults.size())
                                .mapToObj(i -> MonitorDetailExportDto.fromResponseDto(monitorResults.get(i), i + 1))
                                .toList();



                        // 使用EasyExcel生成Excel文件
                        byte[] excelBytes = EasyExcelExportUtil.exportToExcel(exportData, MonitorDetailExportDto.class,
                                        "监控结果");

                        // 设置响应头
                        FileNameUtil.FileNamePair fileNames = FileNameUtil.generateMonitorFileName();
                        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                        response.setHeader("Content-Disposition", fileNames.getContentDisposition());
                        response.setContentLength(excelBytes.length);

                        // 写入响应
                        response.getOutputStream().write(excelBytes);
                        response.getOutputStream().flush();

                        logger.info("Successfully exported {} monitor results to Excel", monitorResults.size());

                } catch (IllegalArgumentException e) {
                        logger.warn("Invalid export request: {}", e.getMessage());
                        try {
                                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                                response.getWriter().write("请求参数无效: " + e.getMessage());
                        } catch (IOException ioException) {
                                logger.error("Error writing error response", ioException);
                        }
                } catch (Exception e) {
                        logger.error("Error exporting monitor results: {}", e.getMessage(), e);
                        try {
                                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                                response.getWriter().write("导出失败: " + e.getMessage());
                        } catch (IOException ioException) {
                                logger.error("Error writing error response", ioException);
                        }
                }
        }

}