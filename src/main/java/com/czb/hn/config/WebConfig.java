package com.czb.hn.config;

import com.czb.hn.interceptor.BillingInterceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 * 处理资源处理器、视图控制器和拦截器的配置
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

        @Autowired
        private UserContextInterceptor userContextInterceptor;

        @Autowired
        private BillingInterceptor billingInterceptor;

        @Override
        public void addResourceHandlers(ResourceHandlerRegistry registry) {
                // 通用的swagger-ui资源配置，不指定具体版本
                registry.addResourceHandler("/swagger-ui/**")
                                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/");

                // 配置swagger-ui页面的访问路径
                registry.addResourceHandler("/swagger-ui.html")
                                .addResourceLocations("classpath:/META-INF/resources/");

                // 处理通用webjars资源
                registry.addResourceHandler("/webjars/**")
                                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        }

        @Override
        public void addViewControllers(ViewControllerRegistry registry) {
                // 配置主要的重定向路径
                registry.addRedirectViewController("/", "/swagger-ui/index.html");
        }

        @Override
        public void addInterceptors(InterceptorRegistry registry) {
                // 1. 首先注册用户上下文拦截器（优先级最高）
                // 负责恢复用户认证信息到ThreadLocal，必须在其他拦截器之前执行
                registry.addInterceptor(userContextInterceptor)
                                .addPathPatterns("/**")
                                .excludePathPatterns(
                                                "/swagger-ui.html",
                                                "/swagger-ui/**",
                                                "/v3/api-docs/**",
                                                "/webjars/**")
                                .order(1); // 设置最高优先级

                // 2. 然后注册计费拦截器（依赖用户上下文）
                // 需要在用户上下文拦截器之后执行，以便能够获取到用户信息
                registry.addInterceptor(billingInterceptor)
                                .addPathPatterns("/**")
                                .excludePathPatterns(
                                                // Swagger相关路径
                                                "/swagger-ui.html",
                                                "/swagger-ui/**",
                                                "/v3/api-docs/**",
                                                "/webjars/**",
                                                // 认证相关路径
                                                "/onepass/**",
                                                "/login/**",
                                                "/logout/**",
                                                // 计费管理路径（避免循环检查）
                                                "/billing/**",
                                                // 分享链接路径
                                                "/share/**",
                                                // 健康检查路径
                                                "/health/**",
                                        "/alert-results/**",
                                        "/monitor/**",
                                                "/actuator/**")
                                .order(2); // 设置较低优先级，确保在用户上下文拦截器之后执行
        }
}