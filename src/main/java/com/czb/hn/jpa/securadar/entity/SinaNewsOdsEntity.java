package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.DetailedSourceType;
import com.czb.hn.enums.EmotionType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MatchType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.ResultViewType;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.ThirdLevelSourceType;
import com.czb.hn.enums.UserVerificationType;
import com.czb.hn.enums.converter.ContentCategoryConverter;
import com.czb.hn.enums.converter.ContentTypeConverter;
import com.czb.hn.enums.converter.DetailedSourceTypeConverter;
import com.czb.hn.enums.converter.EmotionTypeConverter;
import com.czb.hn.enums.converter.InformationSensitivityTypeConverter;
import com.czb.hn.enums.converter.MediaLevelConverter;
import com.czb.hn.enums.converter.MatchTypeConverter;
import com.czb.hn.enums.converter.ResultViewTypeConverter;
import com.czb.hn.enums.converter.SourceTypeConverter;
import com.czb.hn.enums.converter.ThirdLevelSourceTypeConverter;
import com.czb.hn.enums.converter.UserVerificationTypeConverter;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 新浪舆情ODS数据实体
 * 存储从新浪舆情通接口获取的原始数据
 */
@Entity
@Data
@Table(name = "sina_news_ods_test", indexes = {
        @Index(name = "idx_content_id", columnList = "content_id", unique = true),
        @Index(name = "idx_text_id", columnList = "text_id"),
        @Index(name = "idx_publish_time", columnList = "publish_time"),
        @Index(name = "idx_capture_time", columnList = "capture_time"),
        @Index(name = "idx_origin_type", columnList = "origin_type"),
        @Index(name = "idx_processed", columnList = "processed")
})
public class SinaNewsOdsEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "content_id", nullable = false, length = 64, unique = true)
    private String contentId; // 内容ID，唯一标识

    @Column(name = "text_id", length = 64)
    private String textId; // 微博ID或其他平台的内容ID

    @Column(name = "author", length = 255)
    private String author; // 内容作者

    @Column(name = "title", length = 255)
    private String title; // 内容标题

    @Column(name = "text", columnDefinition = "LONGTEXT")
    private String text; // 正文内容

    @Column(name = "summary", columnDefinition = "TEXT")
    private String summary; // 内容摘要

    @Column(name = "url", length = 512)
    private String url; // 原文链接

    @Column(name = "source", length = 64)
    private String source; // 来源（发布设备）

    @Column(name = "source_website", length = 64)
    private String sourceWebsite; // 来源网站

    @Column(name = "capture_website", length = 64)
    private String captureWebsite; // 采集网站

    @Column(name = "publish_time")
    private LocalDateTime publishTime; // 发布时间

    @Column(name = "capture_time")
    private LocalDateTime captureTime; // 采集时间

    @Convert(converter = SourceTypeConverter.class)
    @Column(name = "origin_type")
    private SourceType originType; // 来源类型（hdlt：互动论坛、wb：微博、wx：weixin、zmtapp：客户端、sp：视频、szb：数字报、wz：网站）

    @Convert(converter = DetailedSourceTypeConverter.class)
    @Column(name = "origin_type_second")
    private DetailedSourceType originTypeSecond; // 二级来源类型（央级数字报szbyj、省级数字报szbsj、地市数字报szbdj、其他数字报szbqita等）

    @Convert(converter = ThirdLevelSourceTypeConverter.class)
    @Column(name = "origin_type_third")
    private ThirdLevelSourceType originTypeThird;

    @Convert(converter = MatchTypeConverter.class)
    @Column(name = "match_type")
    private MatchType matchType; // 匹配类型（1-关键词 2-用户）

    @Column(name = "match_info", length = 255)
    private String matchInfo; // 匹配到的内容（如关键词、用户等）

    @Column(name = "match_ticket", length = 64)
    private String matchTicket; // 匹配的方案标识

    @Column(name = "match_name", length = 255)
    private String matchName; // 匹配方案名称

    // 用户信息
    @Column(name = "user_account", length = 64)
    private String userAccount; // 用户账号

    @Column(name = "user_nickname", length = 255)
    private String userNickname; // 用户昵称

    @Column(name = "user_gender", length = 16)
    private String userGender; // 用户性别（m-男，f-女）

    @Column(name = "user_profile_image", length = 512)
    private String userProfileImage; // 用户头像URL

    @Column(name = "user_province", length = 64)
    private String userProvince; // 用户所在省份

    @Column(name = "user_city", length = 64)
    private String userCity; // 用户所在城市

    @Column(name = "user_verified")
    private Boolean userVerified; // 用户是否认证

    @Convert(converter = UserVerificationTypeConverter.class)
    @Column(name = "user_verified_type")
    private UserVerificationType userVerifiedType; // 用户认证类型（-1：普通, 0：橙V, 1-7：蓝V, 200和220：达人，600:金V）

    // 内容扩展信息
    @Convert(converter = InformationSensitivityTypeConverter.class)
    @Column(name = "sensitivity_type")
    private InformationSensitivityType sensitivityType; // 敏感性分类（0：全部 1：敏感 2：非敏感 3:中性）

    @Column(name = "sensitivity_score", length = 16)
    private String sensitivityScore; // 敏感性分值

    @Convert(converter = EmotionTypeConverter.class)
    @Column(name = "emotion")
    private EmotionType emotion; // 情绪（六元情绪：中性，喜悦，悲伤，愤怒，惊奇，恐惧）

    @Convert(converter = ContentCategoryConverter.class)
    @Column(name = "is_original")
    private ContentCategory isOriginal; // 是否原创（1:原创 2:转发）

    @Convert(converter = ResultViewTypeConverter.class)
    @Column(name = "result_view")
    private ResultViewType resultView; // 结果呈现（1:正常，2:噪音）

    @Column(name = "similarity_num")
    private Long similarityNum; // 相似内容数量

    @Column(name = "similarity_tag", length = 64)
    private String similarityTag; // 相似性标签

    @Column(name = "comment_num")
    private Long commentNum; // 评论数

    @Column(name = "forward_num")
    private Long forwardNum; // 转发数

    @Column(name = "second_trades", columnDefinition = "TEXT")
    private String secondTrades; // 行业标签，JSON格式（娱乐，金融，其他等）

    @Convert(converter = ContentTypeConverter.class)
    @Column(name = "content_types")
    private ContentType contentTypes; // 内容类型（1:文本 2:图片 3:短链 4:视频）

    @Column(name = "image_urls", columnDefinition = "TEXT")
    private String imageUrls; // 图片URL，JSON格式

    @Column(name = "video_url", columnDefinition = "TEXT")
    private String videoUrl; // 视频URL

    // 转发内容ID
    @Column(name = "root_content_id", length = 64)
    private String rootContentId; // 根内容ID（转发内容的原始ID）

    // 原始JSON数据
    @Column(name = "original_json", columnDefinition = "LONGTEXT")
    private String originalJson; // 原始JSON数据

    // 处理标记
    @Column(name = "processed")
    private Boolean processed = false; // 是否已处理到DWD层

    // 数据拉取标记
    @Column(name = "data_offset")
    private Long dataOffset; // 数据拉取的offset值（下次可从该值+1开始请求）

    // 修改保留字冲突
    @Column(name = "news_column", length = 255)
    private String column; // 栏目

    @Column(name = "short_url", columnDefinition = "TEXT")
    private String shortUrl; // 短链接，JSON格式

    @Column(name = "home_page", length = 255)
    private String homePage; // 作者主页

    @Column(name = "publish_province", length = 64)
    private String publishProvince; // 发布省份

    @Column(name = "publish_city", length = 64)
    private String publishCity; // 发布城市

    @Column(name = "content_province", length = 512)
    private String contentProvince; // 内容涉及省份

    @Column(name = "content_city", length = 512)
    private String contentCity; // 内容涉及城市

    @Column(name = "location", length = 255)
    private String location; // 定位地

    @Column(name = "filing_province", length = 64)
    private String filingProvince; // 备案省份

    @Column(name = "web_filing_number", length = 255)
    private String webFilingNumber; // 网站备案号

    @Column(name = "web_filing_units", length = 255)
    private String webFilingUnits; // 网站备案单位

    @Column(name = "praise_num")
    private Long praiseNum; // 点赞数

    @Column(name = "share_num")
    private Long shareNum; // 分享数

    @Column(name = "collection_num")
    private Long collectionNum; // 收藏数

    @Column(name = "answer_num")
    private Long answerNum; // 回答数

    @Column(name = "looking_num")
    private Long lookingNum; // 浏览数（viewNum）

    @Column(name = "wb_forward_type", length = 64)
    private String wbForwardType; // 微博转发类型

    @Column(name = "comment_text", columnDefinition = "MEDIUMTEXT")
    private String commentText; // 评论文本

    @Column(name = "comment_forward_text", columnDefinition = "TEXT")
    private String commentForwardText; // 转发评论文本

    @Column(name = "ocr_contents", columnDefinition = "MEDIUMTEXT")
    private String ocrContents; // OCR内容，JSON格式（图片识别内容）

    @Column(name = "annotations", columnDefinition = "TEXT")
    private String annotations; // 注解信息，JSON格式

    @Column(name = "ner_info_ext", columnDefinition = "TEXT")
    private String nerInfoExt; // NER信息扩展，JSON格式

    @Column(name = "video_cover_url", length = 512)
    private String videoCoverUrl; // 视频封面URL（subjectAnalysis）

    @Column(name = "info_category", length = 64)
    private String infoCategory; // 信息分类

    @Convert(converter = MediaLevelConverter.class)
    @Column(name = "info_level")
    private MediaLevel infoLevel; // 信息级别（媒体级别：央级、省级、地市、重点、中小、企业商业）

    @Column(name = "area_involved", length = 255)
    private String areaInvolved; // 涉及区域

    @Column(name = "tags", length = 255)
    private String tags; // 用户标签

    @Column(name = "contents_count")
    private Long contentsCount; // 用户内容数量

    @Column(name = "friends_count")
    private Long friendsCount; // 用户关注数量

    @Column(name = "followers_count")
    private Long followersCount; // 用户粉丝数量

    @Column(name = "create_time", length = 64)
    private String createTime; // 用户创建时间
}