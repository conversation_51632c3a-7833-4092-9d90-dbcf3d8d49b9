package com.czb.hn.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * EasyExcel导出工具类
 * 提供基于EasyExcel的Excel导出功能
 */
public class EasyExcelExportUtil {

    private static final Logger logger = LoggerFactory.getLogger(EasyExcelExportUtil.class);

    /**
     * 导出对象列表到Excel
     *
     * @param dataList 要导出的对象列表
     * @param clazz 数据类型Class
     * @param sheetName 工作表名称
     * @param <T> 数据类型
     * @return Excel文件的字节数组
     */
    public static <T> byte[] exportToExcel(List<T> dataList, Class<T> clazz, String sheetName) {
        if (dataList == null || dataList.isEmpty()) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            EasyExcel.write(outputStream, clazz)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet(sheetName)
                    .doWrite(dataList);
            
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            logger.error("使用EasyExcel导出Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出单个对象到Excel
     *
     * @param data 要导出的对象
     * @param clazz 数据类型Class
     * @param sheetName 工作表名称
     * @param <T> 数据类型
     * @return Excel文件的字节数组
     */
    public static <T> byte[] exportSingleToExcel(T data, Class<T> clazz, String sheetName) {
        if (data == null) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        return exportToExcel(List.of(data), clazz, sheetName);
    }

    /**
     * 导出多个工作表到Excel
     *
     * @param exportData 导出数据列表，每个元素包含数据列表、类型和工作表名称
     * @return Excel文件的字节数组
     */
    public static byte[] exportMultipleSheets(List<ExportSheetData<?>> exportData) {
        if (exportData == null || exportData.isEmpty()) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            var excelWriter = EasyExcel.write(outputStream)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();

            for (int i = 0; i < exportData.size(); i++) {
                ExportSheetData<?> sheetData = exportData.get(i);
                var writeSheet = EasyExcel.writerSheet(i, sheetData.getSheetName())
                        .head(sheetData.getClazz())
                        .build();
                excelWriter.write(sheetData.getData(), writeSheet);
            }

            excelWriter.finish();
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            logger.error("使用EasyExcel导出多工作表Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出工作表数据封装类
     */
    public static class ExportSheetData<T> {
        private final List<T> data;
        private final Class<T> clazz;
        private final String sheetName;

        public ExportSheetData(List<T> data, Class<T> clazz, String sheetName) {
            this.data = data;
            this.clazz = clazz;
            this.sheetName = sheetName;
        }

        public List<T> getData() {
            return data;
        }

        public Class<T> getClazz() {
            return clazz;
        }

        public String getSheetName() {
            return sheetName;
        }
    }
}
