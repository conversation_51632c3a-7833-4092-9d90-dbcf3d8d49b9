package com.czb.hn.util;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件名处理工具类
 * 处理包含中文字符的文件名编码问题
 */
public class FileNameUtil {

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 生成安全的文件名，处理中文字符编码问题
     * 
     * @param chineseName 中文文件名（不包含扩展名）
     * @param englishName 英文文件名（不包含扩展名）
     * @param extension 文件扩展名（如 ".xlsx"）
     * @return 包含安全文件名和编码文件名的对象
     */
    public static FileNamePair generateSafeFileName(String chineseName, String englishName, String extension) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        
        String safeFileName = englishName + "_" + timestamp + extension;
        String chineseFileName = chineseName + "_" + timestamp + extension;
        String encodedFileName = URLEncoder.encode(chineseFileName, StandardCharsets.UTF_8);
        
        return new FileNamePair(safeFileName, encodedFileName);
    }

    /**
     * 生成预警结果文件名
     * 
     * @return 文件名对象
     */
    public static FileNamePair generateAlertFileName() {
        return generateSafeFileName("预警结果", "alert_results", ".xlsx");
    }

    /**
     * 生成监控结果文件名
     * 
     * @return 文件名对象
     */
    public static FileNamePair generateMonitorFileName() {
        return generateSafeFileName("监控结果", "monitor_results", ".xlsx");
    }

    /**
     * 文件名对象，包含安全文件名和编码文件名
     */
    public static class FileNamePair {
        private final String safeFileName;
        private final String encodedFileName;

        public FileNamePair(String safeFileName, String encodedFileName) {
            this.safeFileName = safeFileName;
            this.encodedFileName = encodedFileName;
        }

        public String getSafeFileName() {
            return safeFileName;
        }

        public String getEncodedFileName() {
            return encodedFileName;
        }

        /**
         * 生成完整的Content-Disposition头值
         * 
         * @return Content-Disposition头值
         */
        public String getContentDisposition() {
            return "attachment; filename=\"" + safeFileName + "\"; filename*=UTF-8''" + encodedFileName;
        }
    }
}
