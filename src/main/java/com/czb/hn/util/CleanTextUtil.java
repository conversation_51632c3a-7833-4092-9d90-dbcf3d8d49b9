package com.czb.hn.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CleanTextUtil {
    private static final Logger logger = LoggerFactory.getLogger(CleanTextUtil.class);

    private static final java.util.regex.Pattern HTML_TAG_PATTERN = java.util.regex.Pattern.compile("<[^>]+>");
    private static final java.util.regex.Pattern HTML_ENTITY_PATTERN = java.util.regex.Pattern.compile("&[^;]+;");
    private static final java.util.regex.Pattern URL_PATTERN = java.util.regex.Pattern.compile("https?://\\S+");
    private static final java.util.regex.Pattern EMOJI_PATTERN = java.util.regex.Pattern.compile(
            "[\\uD83C-\\uDBFF\\uDC00-\\uDFFF\\u2600-\\u27BF\\u2300-\\u23FF\\u2B00-\\u2BFF\\u2900-\\u297F\\u3000-\\u303F\\uFE0F]");

    /**
     * 清理文本，去除HTML标签、URL、表情符号等
     * 按照数据仓库清洗标准，确保文本内容的纯净性和编码安全性
     *
     * @param text 待清洗的原始文本
     * @return 清洗后的文本
     */
    public static String cleanText(String text) {
        if (text == null || text.isBlank()) {
            return "";
        }

        String cleanedText = text;

        try {
            // 1. 去除HTML标签
            cleanedText = HTML_TAG_PATTERN.matcher(cleanedText).replaceAll(" ");

            // 2. 处理HTML实体
            cleanedText = cleanedText.replace("&nbsp;", " ")
                    .replace("&amp;", "&")
                    .replace("&lt;", " ")
                    .replace("&gt;", " ")
                    .replace("&quot;", "\"")
                    .replace("&#39;", "'");

            // 3. 去除其他HTML实体
            cleanedText = HTML_ENTITY_PATTERN.matcher(cleanedText).replaceAll(" ");

            // 4. 去除URL
            cleanedText = URL_PATTERN.matcher(cleanedText).replaceAll(" ");

            // 5. 去除表情符号
            cleanedText = EMOJI_PATTERN.matcher(cleanedText).replaceAll("");

            // 6. 去除控制字符
            cleanedText = cleanedText.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

            // 7. 标准化空白字符
            cleanedText = cleanedText.replaceAll("\\s+", " ").trim();

            // 8. 确保编码安全
            cleanedText = ensureEncodingSafety(cleanedText);

            return cleanedText.isBlank() ? "" : cleanedText;

        } catch (Exception e) {
            logger.warn("Error cleaning text, returning safe version: {}", e.getMessage());
            return ensureEncodingSafety(text.replaceAll("\\s+", " ").trim());
        }
    }

    /**
     * 确保字符编码安全性，专注于解决数据库存储问题
     * 保留所有内容，只处理真正导致存储错误的字符
     */
    private static String ensureEncodingSafety(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        try {
            // 只移除真正导致MySQL编码问题的控制字符
            // 保留所有可见字符，包括HTML标签、中文、表情符号等
            String result = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", " ");

            // 确保UTF-8编码安全性
            byte[] utf8Bytes = result.getBytes("UTF-8");
            String utf8String = new String(utf8Bytes, "UTF-8");

            return utf8String;

        } catch (Exception e) {
            logger.warn("Error ensuring encoding safety: {}, returning original text", e.getMessage());
            // 如果处理失败，返回原始文本
            return text;
        }
    }
}
