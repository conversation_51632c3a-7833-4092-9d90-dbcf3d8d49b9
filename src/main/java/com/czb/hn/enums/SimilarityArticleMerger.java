package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 */
public enum SimilarityArticleMerger implements StandardEnum<Integer> {
    MERGER(1, "合并"),
    NOT_MERGER(2, "不合并");

    private final Integer value;
    private final String description;

    SimilarityArticleMerger(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from legacy Boolean value to enum
     *
     * @param isOriginal Legacy Boolean value (true = original, false = forward)
     * @return Corresponding enum value
     */
    public static SimilarityArticleMerger fromBoolean(Boolean isOriginal) {
        if (isOriginal == null) {
            return MERGER; // 默认原创
        }
        return isOriginal ? MERGER : NOT_MERGER;
    }

    /**
     * Convert from string value (matching Elasticsearch document format)
     *
     * @param isOriginal String value ("1" = original, "2" = forward)
     * @return Corresponding enum value
     */
    public static SimilarityArticleMerger fromString(String isOriginal) {
        return StandardEnum.smartConvertWithDefault(SimilarityArticleMerger.class, isOriginal, MERGER);
    }

    /**
     * Convert from integer value (matching Sina API format)
     *
     * @param value Integer value (1 = original, 2 = forward)
     * @return Corresponding enum value
     */
    public static SimilarityArticleMerger fromInteger(Integer value) {
        return StandardEnum.smartConvertWithDefault(SimilarityArticleMerger.class, value, MERGER);
    }

    /**
     * Convert to Boolean for backward compatibility
     *
     * @return Boolean representation (true for ORIGINAL, false for FORWARD)
     */
    public Boolean toBoolean() {
        return switch (this) {
            case MERGER -> true;
            case NOT_MERGER -> false;
        };
    }

    /**
     * Get integer value for API compatibility
     *
     * @return Integer value (1 = original, 2 = forward)
     */
    public Integer toInteger() {
        return getValue(); // 直接返回value值
    }

    /**
     * Check if this is original content
     * 
     * @return true if original, false otherwise
     */
    public boolean isOriginal() {
        return this == MERGER;
    }

    /**
     * Check if this is forwarded content
     * 
     * @return true if forwarded, false otherwise
     */
    public boolean isForward() {
        return this == NOT_MERGER;
    }
}
