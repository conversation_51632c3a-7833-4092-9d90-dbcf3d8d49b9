package com.czb.hn.enums.converter;

import com.czb.hn.enums.ThirdLevelSourceType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for DetailedSourceType
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 */
@Converter(autoApply = true)
public class ThirdLevelSourceTypeConverter implements AttributeConverter<ThirdLevelSourceType, String> {

    @Override
    public String convertToDatabaseColumn(ThirdLevelSourceType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public ThirdLevelSourceType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return ThirdLevelSourceType.fromString(dbData);
    }
}
