package com.czb.hn.dto.recipients;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO for email notification configuration
 */
public record EmailConfigDto(
        @Schema(description = "Whether email notifications are enabled", example = "true")
        boolean enabled,

        @Schema(description = "Email recipients list")
        List<EmailRecipientDto> recipients
) {
    public EmailConfigDto {
        if (enabled && (recipients == null || recipients.isEmpty())) {
            throw new IllegalArgumentException("Email recipients cannot be empty when email is enabled");
        }
        if (recipients != null && recipients.size() > 20) {
            throw new IllegalArgumentException("Cannot have more than 20 email recipients");
        }
    }
}
