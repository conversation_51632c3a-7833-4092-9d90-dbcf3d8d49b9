package com.czb.hn.dto.recipients;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO for email recipient
 */
public record EmailRecipientDto(
        @Schema(description = "isActive", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
        Boolean isActive,

        @Schema(description = "Recipient id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
        Long id,

        @Schema(description = "Recipient name", example = "John Doe", requiredMode = Schema.RequiredMode.REQUIRED)
        String name,

        @Schema(description = "Email address", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
        String email
) {
    public EmailRecipientDto {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Recipient name cannot be null or blank");
        }
        if (name.length() > 100) {
            throw new IllegalArgumentException("Recipient name cannot exceed 100 characters");
        }
        if (id == null) {
            throw new IllegalArgumentException("Recipient id cannot be null or blank");
        }

        if (email == null || email.isBlank()) {
            throw new IllegalArgumentException("Email address cannot be null or blank");
        }
        if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
            throw new IllegalArgumentException("Invalid email address format");
        }
    }
}
