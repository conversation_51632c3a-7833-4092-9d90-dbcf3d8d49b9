package com.czb.hn.dto.recipients;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO for SMS recipient
 */
public record SmsRecipientDto(
        @Schema(description = "isActive", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
        Boolean isActive,

        @Schema(description = "Recipient id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
        Long id,

        @Schema(description = "Recipient name", example = "John Do<PERSON>", requiredMode = Schema.RequiredMode.REQUIRED)
        String name,

        @Schema(description = "Phone number", example = "13800138000", requiredMode = Schema.RequiredMode.REQUIRED)
        String phone
) {
    public SmsRecipientDto {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Recipient name cannot be null or blank");
        }
        if (name.length() > 100) {
            throw new IllegalArgumentException("Recipient name cannot exceed 100 characters");
        }

        if (id == null) {
            throw new IllegalArgumentException("Recipient id cannot be null or blank");
        }

        if (phone == null || phone.isBlank()) {
            throw new IllegalArgumentException("Phone number cannot be null or blank");
        }
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            throw new IllegalArgumentException("Invalid phone number format (must be Chinese mobile number)");
        }
    }
}
