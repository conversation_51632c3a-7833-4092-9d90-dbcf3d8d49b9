package com.czb.hn.dto.bulletin;

import com.czb.hn.dto.recipients.ReceiveInfoBulletinDto;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2025/07/12  11:10
 */
public record ReceiveInfoBulletinPrepareDto (
        @Schema(description = "邮箱")
        ReceiveInfoBulletinDto emailReceiveInfoBulletinDto,
        @Schema(description = "短信")
        ReceiveInfoBulletinDto smsReceiveInfoBulletinDto

){


}
