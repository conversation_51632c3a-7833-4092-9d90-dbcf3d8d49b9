package com.czb.hn.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "企业信息DTO")
public record EnterpriseDTO(
        @Schema(description = "企业ID", example = "enterprise123", requiredMode = Schema.RequiredMode.REQUIRED) String id,

        @Schema(description = "企业名称", example = "华能资本服务有限公司", requiredMode = Schema.RequiredMode.REQUIRED) String name) {
    public EnterpriseDTO {
        if (id == null) {
            throw new IllegalArgumentException("Enterprise ID cannot be null");
        }
        if (name == null) {
            throw new IllegalArgumentException("Enterprise name cannot be null");
        }
    }
}