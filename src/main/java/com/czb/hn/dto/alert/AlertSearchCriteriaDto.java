package com.czb.hn.dto.alert;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * Alert Search Criteria DTO
 * 预警信息搜索条件DTO，支持多维度过滤和全文检索
 */
@Schema
public record AlertSearchCriteriaDto(

        @Schema(description = "方案ID", example = "1") Long planId,

        @Schema(description = "信息属性", example = "1", allowableValues = {
                "1", "2", "3" }) Integer informationSensitivityType,

        @Schema(description = "预警级别", example = "SEVERE", allowableValues = {
                "GENERAL", "MODERATE", "SEVERE" }) String warningLevel,

        @Schema(description = "来源类型", example = "wb", allowableValues = {
                "hdlt", "wb", "wx", "zmtapp", "sp", "szb", "wz" }) String sourceType,

        @Schema(description = "开始时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00") @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,

        @Schema(description = "结束时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 18:30:00") @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,

        @Schema(description = "搜索文本（标题和正文全文检索）", example = "重要新闻") String searchText,

        @Schema(description = "页码", example = "1") @Min(value = 1, message = "页码不能小于1") Integer page,

        @Schema(description = "页大小", example = "20") @Min(value = 1, message = "页大小不能小于1") @Max(value = 100, message = "页大小不能超过100") Integer size){
    /**
     * 紧凑构造函数，设置默认值和验证
     */
    public AlertSearchCriteriaDto {
        // 设置默认值
        if (page == null)
            page = 1;
        if (size == null)
            size = 20;

        // 将空字符串转换为null
        if (informationSensitivityType != null ) {
            informationSensitivityType = null;
        }
        if (warningLevel != null && warningLevel.trim().isEmpty()) {
            warningLevel = null;
        }
        if (sourceType != null && sourceType.trim().isEmpty()) {
            sourceType = null;
        }
        if (searchText != null && searchText.trim().isEmpty()) {
            searchText = null;
        }

        // 验证时间范围
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
    }

}
