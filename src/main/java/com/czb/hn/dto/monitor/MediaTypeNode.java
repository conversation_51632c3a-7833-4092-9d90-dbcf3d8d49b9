package com.czb.hn.dto.monitor;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 媒体类型树形节点DTO
 * 用于表示媒体类型的层级结构
 */
@Schema(description = "媒体类型树形节点")
public record MediaTypeNode(
        @Schema(description = "媒体类型代码", example = "hdlt") String code,

        @Schema(description = "媒体类型名称", example = "互动论坛") String name,

        @Schema(description = "父级代码", example = "hdlt") String parentCode,

        @Schema(description = "层级 (1=一级, 2=二级, 3=三级)", example = "1") int level,

        @Schema(description = "是否被选中", example = "true") boolean selected,

        @Schema(description = "子节点列表") List<MediaTypeNode> children) implements Serializable {

    // Compact canonical constructor for validation
    public MediaTypeNode {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Media type code cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Media type name cannot be null or empty");
        }
        if (level < 1 || level > 3) {
            throw new IllegalArgumentException("Level must be between 1 and 3");
        }
        if (children == null) {
            children = List.of();
        }
    }

    /**
     * 创建一级媒体类型节点
     */
    public static MediaTypeNode createFirstLevel(String code, String name, boolean selected) {
        return new MediaTypeNode(code, name, null, 1, selected, List.of());
    }

    /**
     * 创建二级媒体类型节点
     */
    public static MediaTypeNode createSecondLevel(String code, String name, String parentCode, boolean selected) {
        return new MediaTypeNode(code, name, parentCode, 2, selected, List.of());
    }

    /**
     * 创建三级媒体类型节点
     */
    public static MediaTypeNode createThirdLevel(String code, String name, String parentCode, boolean selected) {
        return new MediaTypeNode(code, name, parentCode, 3, selected, List.of());
    }

    /**
     * 添加子节点，返回新的节点实例
     */
    public MediaTypeNode withChild(MediaTypeNode child) {
        List<MediaTypeNode> newChildren = new java.util.ArrayList<>(this.children);
        newChildren.add(child);
        return new MediaTypeNode(this.code, this.name, this.parentCode, this.level, this.selected, newChildren);
    }

    /**
     * 添加多个子节点，返回新的节点实例
     */
    public MediaTypeNode withChildren(List<MediaTypeNode> newChildren) {
        List<MediaTypeNode> allChildren = new java.util.ArrayList<>(this.children);
        allChildren.addAll(newChildren);
        return new MediaTypeNode(this.code, this.name, this.parentCode, this.level, this.selected, allChildren);
    }

    /**
     * 检查是否有选中的子节点
     */
    @com.fasterxml.jackson.annotation.JsonIgnore
    public boolean hasSelectedChildren() {
        return hasSelectedChildren(new java.util.HashSet<>());
    }

    /**
     * 递归检查是否有选中的子节点，使用Set防止无限递归
     */
    private boolean hasSelectedChildren(java.util.Set<String> visited) {
        if (visited.contains(this.code)) {
            return false; // 防止循环引用
        }
        visited.add(this.code);

        return children.stream().anyMatch(child -> child.selected || child.hasSelectedChildren(visited));
    }

    /**
     * 获取所有选中的节点（包括自身和子节点）
     * 注意：此方法仅用于内部逻辑，不参与JSON序列化
     */
    @com.fasterxml.jackson.annotation.JsonIgnore
    public List<MediaTypeNode> getSelectedNodes() {
        List<MediaTypeNode> selectedNodes = new java.util.ArrayList<>();
        collectSelectedNodes(selectedNodes, new java.util.HashSet<>());
        return selectedNodes;
    }

    /**
     * 递归收集选中的节点，使用Set防止无限递归
     */
    private void collectSelectedNodes(List<MediaTypeNode> result, java.util.Set<String> visited) {
        if (visited.contains(this.code)) {
            return; // 防止循环引用
        }
        visited.add(this.code);

        if (this.selected) {
            result.add(this);
        }
        for (MediaTypeNode child : children) {
            child.collectSelectedNodes(result, visited);
        }
    }

    /**
     * 检查是否为根节点
     */
    public boolean isRoot() {
        return parentCode == null || parentCode.trim().isEmpty();
    }

    /**
     * 检查是否为叶子节点
     */
    public boolean isLeaf() {
        return children.isEmpty();
    }
}
