package com.czb.hn.dto.sina.data;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 新浪舆情通内容数据DTO
 */
public record SinaNewsContentDto(
        String contentId,
        String author,
        String title,
        String text,
        String textId,
        String summary,
        String url,
        String source,
        String sourceWebsite,
        String captureWebsite,
        String publishTime,
        String captureTime,
        @JsonProperty("shortUrl") List<String> shortUrl,
        String originType,
        String originTypeSecond,
        String originTypeThird,
        SinaNewsMatchInfoDto matchInfo,
        SinaNewsUserExtDto userExt,
        SinaNewsContentExtDto contentExt,
        SinaNewsVideoExtDto videoExt,
        SinaNewsContentDto rootContent) {
    /**
     * 匹配信息
     */
    public record SinaNewsMatchInfoDto(
            String type,
            String info,
            String ticket,
            String name) {
    }

    /**
     * 用户扩展信息
     */
    public record SinaNewsUserExtDto(
            String accountNum,
            String nickname,
            String gender,
            String profileImageUrl,
            String homePage,
            String province,
            String city,
            Boolean verified,
            String verifiedType,
            String tags,
            Long contentsCount,
            Long friendsCount,
            Long followersCount,
            String createTime,
            String accountVerifyInfo,
            String userId,
            String userAddress) {
    }

    /**
     * 内容扩展信息
     */
    public record SinaNewsContentExtDto(
            Integer sensitivityType,
            String sensitivityScore,
            String emotion,
            String isOriginal,
            String isNormarlData,
            Long similarityNum,
            String similarityTag,
            String publishProvince,
            String publishCity,
            String contentProvince,
            String contentCity,
            Long commentNum,
            Long forwardNum,
            Long lookingNum,
            Long praiseNum,
            Long viewNum,
            Long shareNum,
            Long answerNum,
            Long collectionNum,
            String location,
            String annotations,
            String rootAnnotations,
            String newOriginType,
            String originLevel,
            Integer wbForwardType,
            List<String> secondTrades,
            String contentTypes,
            String ocrContents,
            @JsonProperty("images") List<SinaNewsImageDto> images,
            String commentText,
            String commentForwardText,
            String filingProvence,
            String webFilingNumber,
            String webFilingUnits,
            String sourceArea) {
    }

    /**
     * 图片信息
     */
    public record SinaNewsImageDto(
            String thumbnail,
            String bmiddle,
            String large) {
    }

    /**
     * 视频扩展信息
     */
    public record SinaNewsVideoExtDto(
            String videoOriginUrl,
            String subjectAnalysis,
            String videoSubtitleText,
            String videoStaticSubtitleText,
            String videoDynamicSubtitleText,
            String videoAudioText,
            String videoBackgroundText,
            String infoCategory,
            String infoLevel,
            String areaInvolved) {
    }
}