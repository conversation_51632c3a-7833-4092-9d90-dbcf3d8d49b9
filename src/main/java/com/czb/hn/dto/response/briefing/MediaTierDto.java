package com.czb.hn.dto.response.briefing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MediaTierDto {
    @Schema(description = "媒体级别")
    private String mediaTier;

    @Schema(description = "数量")
    private long mediaCount;
}
