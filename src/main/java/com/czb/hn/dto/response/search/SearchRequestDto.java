package com.czb.hn.dto.response.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@Schema(description = "新浪舆情数据搜索请求对象")
public class SearchRequestDto {

    @Schema(description = "方案ID")
    Long planId;

    @Schema(description = "开始时间")
    String startTime;

    @Schema(description = "结束时间")
    String endTime;

    @Schema(description = "排序规则")
    Integer sortRule;

    @Schema(description = "信息属性")
    List<Integer> sensitivityType;

    @Schema(description = "相似文章呈现规则")
    Boolean similarityDisplayRule;

    @Schema(description = "匹配方式")
    Integer matchMethod;

    @Schema(description = "媒体类型")
    List<String> mediaTypes;

    @Schema(description = "二级来源类型")
    List<String> mediaTypeSecond;

    @Schema(description = "三级来源类型")
    List<String> mediaTypeThirds;

    @Schema(description = "内容包含")
    List<Integer> contentType;

    @Schema(description = "内容类型")
    List<Integer> isOriginal;

    @Schema(description = "图文识别")
    Integer imageTextMode;

    @Schema(description = "行业信息")
    List<String> secondTrades;

    @Schema(description = "粉丝数下限")
    Long authorFollowersCountMin;

    @Schema(description = "粉丝数上限")
    Long authorFollowersCountMax;

    @Schema(description = "信源级别")
    List<String> mediaLevel;
}
