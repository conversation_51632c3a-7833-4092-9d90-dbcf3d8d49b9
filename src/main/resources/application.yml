# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
    session:
      cookie:
        name: SECURADAR_SESSION # 自定义会话cookie名称
        http-only: true # 防止JavaScript访问cookie
        secure: true # 开发环境可以设为false，生产环境应设为true
        path: /api # 确保cookie路径与context-path一致
        max-age: 3600 # 1小时的cookie有效期(秒)
      timeout: 3600 # 会话超时时间(秒)

# Spring配置
spring:
  application:
    name: SecuRadar
  datasource:
    primary:
      url: jdbc:mysql://${MYSQL_HOST:************}:${MYSQL_PORT:3306}/securadar?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
      username: dev
      password: ycjf_2018
      driver-class-name: com.mysql.cj.jdbc.Driver
    secondary:
      url: jdbc:mysql://${SECONDARY_MYSQL_HOST:************}:${SECONDARY_MYSQL_PORT:3306}/hnylt?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
      username: dev
      password: ycjf_2018
      driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

# SpringDoc OpenAPI 配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    disable-swagger-default-url: false
    displayRequestDuration: true
    enabled: true
  api-docs:
    path: /v3/api-docs
    enabled: true

# 定时任务配置
schedule:
  collector:
    enabled: false # 是否启用数据收集定时任务
    cron: "0 */10 * * * *" # 每10分钟执行一次数据拉取
    environment: "production" # 环境标识
    batch-size: 500 # 批处理大小
    max-retries: 3 # 最大重试次数
    retry-interval: 5000 # 重试间隔（毫秒）
  cleaner:
    enabled: false # 是否启用数据清洗定时任务
    cron: "0 */15 * * * *" # 每15分钟执行一次数据清洗
    environment: "production" # 环境标识
    batch-size: 100 # 批处理大小
  aggregator:
    enabled: false # 是否启用数据聚合定时任务
    cron: "0 0 1 * * *" # 每天凌晨1点执行聚合
    environment: "production" # 环境标识
  elasticsearch-sync:
    enabled: true # 是否启用Elasticsearch同步定时任务
    cron: "0 */5 * * * *" # 每5分钟同步一次数据到Elasticsearch
    environment: "production" # 环境标识
    batch-size: 100 # 批处理大小
  billing:
    enabled: true # 是否启用计费系统定时任务
    expiration-check-cron: "0 0 2 * * ?" # 每天凌晨2点检查过期订阅
    expiration-warning-cron: "0 0 9 * * ?" # 每天上午9点发送过期警告
    environment: "production" # 环境标识
    warning-days: 7 # 过期警告提前天数

# 新浪舆情通API配置
sina:
  api:
    auth:
      url: https://api-open-wx-www.yqt365.com/dataapp/api
    appId: ${SINA_APP_ID:your_app_id}
    appSecret: ${SINA_APP_SECRET:your_app_secret}
    default:
      ticket: ${SINA_DEFAULT_TICKET:your_default_ticket}
      fetch:
        limit: 500
    data:
      url: https://api-open-wx-www.yqt365.com/dataapp/api/data/kafka/scribe/poll

  # 令牌缓存配置
  token:
    cache:
      cleanup:
        days: 7 # 清理过期令牌的天数
    encryption:
      key: ${SINA_TOKEN_ENCRYPTION_KEY:SecuRadar2024TokenEncryptionKey32} # 令牌加密密钥

  # 数据处理配置
  data:
    process:
      batch-size: 100

  # 数据聚合配置
  aggregator:
    topk:
      default: 10

# Elasticsearch配置
elasticsearch:
  host: ${ELASTICSEARCH_HOST:***********}
  port: ${ELASTICSEARCH_PORT:9200}
  username: ${ELASTICSEARCH_USERNAME:elastic}
  password: ${ELASTICSEARCH_PASSWORD:Hz&11_djook}
  batch:
    size: 100
  index:
    shards: 3
    replicas: 1

# 日志配置
logging:
  level:
    root: INFO
    com.czb.hn: INFO
    org.hibernate.SQL: INFO
    org.springframework.security: INFO # 添加Spring Security调试日志
    org.springdoc: INFO # 添加SpringDoc调试日志
  file:
    name: logs/securadar.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 安全配置
onepass:
  issuer: ${ONEPASS_ISSUER:https://onepass-auth-server-ycjf-test.ycfin.net/realm/f7c125e9a0ae4399b98d1d1f2bb9ab76}
  client-id: ${ONEPASS_CLIENT_ID:0407725a7a7f4cf984043da156f7b3cd}
  client-secret: ${ONEPASS_CLIENT_SECRET:i6cXM7Zt4X5yrVT7-GI7nGWvU2JLZMCiuPlErN2TAUo}
  redirect-uri: ${ONEPASS_REDIRECT_URI:http://127.0.0.1:8080/api/onepass/login/code}
  # 组织信息缓存配置
  group:
    cache:
      ttl: ${ONEPASS_GROUP_CACHE_TTL:3600000} # 缓存有效期（毫秒，默认1小时）
      refresh: ${ONEPASS_GROUP_CACHE_REFRESH:1800000} # 缓存定时刷新间隔（毫秒，默认30分钟）

share:
  protected-urls: /api/share/**
pdf:
  service:
    base-url: https://pdf-renderer-ycjf-test.ycfin.net/
    timeout: 60
bulletin:
  briefing:
    # base-url: https://zmyq-ycjf-test.ycfin.net/
    base-url:

minio:
  endpoint: http://***********:9000
  accessKey: p0vOXua3OSzlxBoOYrKx
  secretKey: 1fpt9WH2zKXAhhEbpQ2npguvz9xR2bN692hcU0HQ
  bucketName: securadar

  # sms
sms:
  url: ${SMS_SERVICE_ENDPOINT:http://hnczb-sms-svc:8080/api/smsnotice}
  newUrl: ${SMS_SERVICE_ENDPOINT:http://hnczb-sms-svc:8080/api/sms}
  connectTimeout: 30000
  socketTimeout: 30000
mail:
  from: <EMAIL>
  username: <EMAIL>
  password: BBNXOXWXUKVMYUZW
  host: smtp.163.com
  port: "465"

alert:
  message:
    sms:
      template:
        id: 172
        content: "%s 监控方案 %s 新增%d条预警"
        noWarnContent: "监测方案%s在%s无预警事件"
    email:
      template:
        subjectContent: "【%s】新增%d条预警: %s"
        noWarnSubjectContent: "【%s】%s 无预警事件"
    webHookUrl: ${WEBHOOK_URL:http://127.0.0.1:8080/api/alert}
